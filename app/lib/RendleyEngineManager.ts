// Rendley Engine Manager - Singleton pattern untuk mengelola engine instance
class RendleyEngineManager {
  private static instance: RendleyEngineManager;
  private engine: any = null;
  private isInitialized: boolean = false;
  private initPromise: Promise<any> | null = null;

  private constructor() {}

  static getInstance(): RendleyEngineManager {
    if (!RendleyEngineManager.instance) {
      RendleyEngineManager.instance = new RendleyEngineManager();
    }
    return RendleyEngineManager.instance;
  }

  async initializeEngine(canvasElement: HTMLCanvasElement): Promise<any> {
    // <PERSON><PERSON> sudah ada proses inisialisasi yang berjalan, tunggu selesai
    if (this.initPromise) {
      return this.initPromise;
    }

    // Jika sudah diinisialisasi, return engine yang ada
    if (this.isInitialized && this.engine) {
      try {
        // Update canvas jika berbeda
        this.engine.updateDisplay({
          view: canvasElement,
          width: 1080,
          height: 1920,
          backgroundColor: '#000000',
        });
      } catch (err) {
        console.log('Could not update display:', err);
      }
      return this.engine;
    }

    // <PERSON><PERSON> proses inisialisasi baru
    this.initPromise = this.doInitialize(canvasElement);
    
    try {
      const engine = await this.initPromise;
      this.engine = engine;
      this.isInitialized = true;
      return engine;
    } catch (error) {
      this.initPromise = null;
      throw error;
    }
  }

  private async doInitialize(canvasElement: HTMLCanvasElement): Promise<any> {
    try {
      // Dynamic import untuk menghindari SSR issues
      const { Engine } = await import('@rendley/sdk');

      // Cek apakah engine sudah diinisialisasi di level SDK
      if (Engine.isInitialized && Engine.isInitialized()) {
        console.log('Engine already initialized at SDK level, getting instance');
        const existingEngine = Engine.getInstance();
        
        // Update display untuk canvas baru
        try {
          existingEngine.updateDisplay({
            view: canvasElement,
            width: 1080,
            height: 1920,
            backgroundColor: '#000000',
          });
        } catch (updateErr) {
          console.log('Could not update display:', updateErr);
        }
        
        return existingEngine;
      }

      // Inisialisasi engine baru
      console.log('Initializing new Rendley engine');
      const engine = Engine.getInstance().init({
        license: {
          licenseName: 'mides',
          licenseKey: '54CB577408EA73C950FA0001',
        },
        display: {
          width: 1080,
          height: 1920,
          backgroundColor: '#000000',
          view: canvasElement,
        },
      });

      console.log('Rendley engine initialized successfully');
      return engine;

    } catch (error) {
      console.error('Failed to initialize Rendley engine:', error);
      throw error;
    }
  }

  getEngine(): any {
    return this.engine;
  }

  isEngineInitialized(): boolean {
    return this.isInitialized && this.engine !== null;
  }

  // Method untuk reset engine jika diperlukan
  resetEngine(): void {
    this.engine = null;
    this.isInitialized = false;
    this.initPromise = null;
  }

  // Method untuk cleanup
  cleanup(): void {
    if (this.engine) {
      try {
        // Tambahkan cleanup logic jika Rendley menyediakan
        // this.engine.destroy();
      } catch (err) {
        console.error('Error during engine cleanup:', err);
      }
    }
    this.resetEngine();
  }
}

export default RendleyEngineManager;
