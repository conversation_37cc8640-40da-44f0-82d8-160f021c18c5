import Image from "next/image";
import SimpleRendleyUI from "./components/SimpleRendleyUI";

export default function Home() {
  return (
    <div className="min-h-screen p-4 sm:p-8 pb-20 font-[family-name:var(--font-geist-sans)]">
      <main className="flex flex-col gap-8 items-center">
        <div className="text-center">
          <Image
            className="dark:invert mx-auto mb-4"
            src="/next.svg"
            alt="Next.js logo"
            width={180}
            height={38}
            priority
          />
          <h1 className="text-4xl font-bold mb-4">🎬 Rendley Video Editor</h1>
          <p className="text-gray-600 mb-8 text-lg">
            Professional video editing powered by Rendley SDK
          </p>
          <div className="inline-flex items-center px-4 py-2 bg-green-100 text-green-800 rounded-full text-sm font-medium">
            ✅ License: mides | Ready to create videos
          </div>
        </div>

        {/* Simple Rendley UI - Main Component */}
        <div className="w-full">
          <SimpleRendleyUI />
        </div>
      </main>
    </div>
  );
}
