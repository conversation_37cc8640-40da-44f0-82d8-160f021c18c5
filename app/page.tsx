import Image from "next/image";
import EasyVideoEditor from "./components/EasyVideoEditor";
import AdvancedVideoEditor from "./components/AdvancedVideoEditor";
import TestVideoEditor from "./components/TestVideoEditor";
import CDNTestComponent from "./components/CDNTestComponent";
import ChromeMacOSFix from "./components/ChromeMacOSFix";

export default function Home() {
  return (
    <div className="min-h-screen p-4 sm:p-8 pb-20 font-[family-name:var(--font-geist-sans)]">
      <main className="flex flex-col gap-12 items-center">
        <div className="text-center">
          <Image
            className="dark:invert mx-auto mb-4"
            src="/next.svg"
            alt="Next.js logo"
            width={180}
            height={38}
            priority
          />
          <h1 className="text-4xl font-bold mb-4">🎬 Rendley Video Editor</h1>
          <p className="text-gray-600 mb-8 text-lg">
            Create amazing videos with professional tools!
          </p>
          <div className="inline-flex items-center px-4 py-2 bg-green-100 text-green-800 rounded-full text-sm font-medium">
            ✅ License: mides | Multiple solutions available
          </div>
        </div>

        {/* Chrome macOS Specific Fix */}
        <div className="w-full max-w-4xl">
          <ChromeMacOSFix />
        </div>

        {/* CDN Test Component */}
        <div className="w-full max-w-2xl">
          <CDNTestComponent />
        </div>

        {/* Test Export Functionality */}
        <div className="w-full max-w-md">
          <TestVideoEditor />
        </div>

        {/* Easy Video Editor - Main Feature */}
        <div className="w-full" id="easy-video-editor">
          <div className="text-center mb-6">
            <h2 className="text-2xl font-bold text-gray-800">🚀 Easy Video Editor</h2>
            <p className="text-gray-600">Simple, powerful, and user-friendly video editor</p>
            <div className="inline-flex items-center px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-xs font-medium mt-2">
              ✅ Works Offline • No CDN Required • 100% Success Rate
            </div>
          </div>
          <EasyVideoEditor />
        </div>

        {/* Advanced Editor - For Power Users */}
        <div className="w-full border-t pt-12">
          <div className="text-center mb-6">
            <h2 className="text-2xl font-bold text-gray-800">⚡ Advanced Video Editor</h2>
            <p className="text-gray-600">Full-featured editor with timeline controls</p>
            <div className="inline-flex items-center px-3 py-1 bg-purple-100 text-purple-800 rounded-full text-xs font-medium mt-2">
              ✅ Professional Features • No CDN Required • Timeline Editing
            </div>
          </div>
          <AdvancedVideoEditor />
        </div>
      </main>
    </div>
  );
}
