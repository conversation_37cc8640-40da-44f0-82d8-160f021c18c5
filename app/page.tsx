import Image from "next/image";
import RendleyVideoEditor from "./components/RendleyVideoEditor";

export default function Home() {
  return (
    <div className="min-h-screen p-8 pb-20 gap-16 sm:p-20 font-[family-name:var(--font-geist-sans)]">
      <main className="flex flex-col gap-[32px] items-center">
        <div className="text-center">
          <Image
            className="dark:invert mx-auto mb-4"
            src="/next.svg"
            alt="Next.js logo"
            width={180}
            height={38}
            priority
          />
          <h1 className="text-3xl font-bold mb-4">Next.js + Rendley Video Editor</h1>
          <p className="text-gray-600 mb-8">
            A powerful video editing experience powered by Rendley SDK
          </p>
        </div>

        {/* Rendley Video Editor Component */}
        <div className="w-full max-w-4xl">
          <RendleyVideoEditor
            width={1080}
            height={1920}
            backgroundColor="#000000"
          />
        </div>

        <div className="text-center mt-8">
          <h2 className="text-xl font-semibold mb-4">Getting Started</h2>
          <ol className="list-inside list-decimal text-sm/6 text-left font-[family-name:var(--font-geist-mono)] max-w-2xl">
            <li className="mb-2 tracking-[-.01em]">
              Get your Rendley license from{" "}
              <a
                href="https://app.rendley.com/"
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 hover:underline"
              >
                https://app.rendley.com/
              </a>
            </li>
            <li className="mb-2 tracking-[-.01em]">
              Update the{" "}
              <code className="bg-black/[.05] dark:bg-white/[.06] px-1 py-0.5 rounded font-[family-name:var(--font-geist-mono)] font-semibold">
                .env.local
              </code>{" "}
              file with your license credentials
            </li>
            <li className="tracking-[-.01em]">
              Restart the development server to see the video editor in action
            </li>
          </ol>
        </div>

        <div className="flex gap-4 items-center flex-col sm:flex-row mt-8">
          <a
            className="rounded-full border border-solid border-transparent transition-colors flex items-center justify-center bg-foreground text-background gap-2 hover:bg-[#383838] dark:hover:bg-[#ccc] font-medium text-sm sm:text-base h-10 sm:h-12 px-4 sm:px-5 sm:w-auto"
            href="https://docs.rendley.com/"
            target="_blank"
            rel="noopener noreferrer"
          >
            📚 Rendley Docs
          </a>
          <a
            className="rounded-full border border-solid border-black/[.08] dark:border-white/[.145] transition-colors flex items-center justify-center hover:bg-[#f2f2f2] dark:hover:bg-[#1a1a1a] hover:border-transparent font-medium text-sm sm:text-base h-10 sm:h-12 px-4 sm:px-5 w-full sm:w-auto md:w-[158px]"
            href="https://playground.rendley.com"
            target="_blank"
            rel="noopener noreferrer"
          >
            🎮 Playground
          </a>
        </div>
      </main>

      <footer className="flex gap-[24px] flex-wrap items-center justify-center mt-16">
        <a
          className="flex items-center gap-2 hover:underline hover:underline-offset-4"
          href="https://docs.rendley.com/quick-start/create-first-video.html"
          target="_blank"
          rel="noopener noreferrer"
        >
          <Image
            aria-hidden
            src="/file.svg"
            alt="File icon"
            width={16}
            height={16}
          />
          Create First Video
        </a>
        <a
          className="flex items-center gap-2 hover:underline hover:underline-offset-4"
          href="https://docs.rendley.com/examples/slideshow-video.html"
          target="_blank"
          rel="noopener noreferrer"
        >
          <Image
            aria-hidden
            src="/window.svg"
            alt="Window icon"
            width={16}
            height={16}
          />
          Examples
        </a>
        <a
          className="flex items-center gap-2 hover:underline hover:underline-offset-4"
          href="https://discord.gg/BwdeFFEVXR"
          target="_blank"
          rel="noopener noreferrer"
        >
          <Image
            aria-hidden
            src="/globe.svg"
            alt="Globe icon"
            width={16}
            height={16}
          />
          Discord Community
        </a>
      </footer>
    </div>
  );
}
