'use client';

import { useEffect, useRef, useState } from 'react';

declare global {
  namespace JSX {
    interface IntrinsicElements {
      'rendley-video-editor': {
        id?: string;
        licensename?: string;
        licensekey?: string;
        pexelsapikey?: string;
        giphyapikey?: string;
        theme?: 'light' | 'dark';
        ref?: React.RefObject<HTMLElement>;
      };
    }
  }
}

interface RendleyVideoEditorUIProps {
  theme?: 'light' | 'dark';
  pexelsApiKey?: string;
  giphyApiKey?: string;
}

export default function RendleyVideoEditorUI({ 
  theme = 'dark',
  pexelsApiKey = '',
  giphyApiKey = ''
}: RendleyVideoEditorUIProps) {
  const editorRef = useRef<HTMLElement>(null);
  const [isLoaded, setIsLoaded] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadRendleyUI = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Check if already loaded
        if (window.customElements && window.customElements.get('rendley-video-editor')) {
          console.log('Rendley Video Editor already loaded');
          setIsLoaded(true);
          setIsLoading(false);
          return;
        }

        // Load the Rendley Video Editor UI script
        const script = document.createElement('script');
        script.type = 'module';
        script.innerHTML = `
          import { defineCustomElements } from "https://cdn.rendley.com/sdk/video-editor/1.0.0/loader/index.js";
          
          defineCustomElements().then(() => {
            console.log('Rendley Video Editor UI loaded successfully');
            window.dispatchEvent(new CustomEvent('rendley-loaded'));
          }).catch((err) => {
            console.error('Failed to load Rendley Video Editor UI:', err);
            window.dispatchEvent(new CustomEvent('rendley-error', { detail: err }));
          });
        `;

        // Add event listeners
        const handleLoaded = () => {
          setIsLoaded(true);
          setIsLoading(false);
          console.log('Rendley Video Editor UI is ready');
        };

        const handleError = (event: any) => {
          const errorMsg = event.detail?.message || 'Failed to load Rendley Video Editor UI';
          setError(errorMsg);
          setIsLoading(false);
          console.error('Rendley load error:', errorMsg);
        };

        window.addEventListener('rendley-loaded', handleLoaded);
        window.addEventListener('rendley-error', handleError);

        // Append script to head
        document.head.appendChild(script);

        // Cleanup function
        return () => {
          window.removeEventListener('rendley-loaded', handleLoaded);
          window.removeEventListener('rendley-error', handleError);
          // Note: We don't remove the script as it might be used by other components
        };

      } catch (err) {
        console.error('Error loading Rendley UI:', err);
        setError(err instanceof Error ? err.message : 'Unknown error');
        setIsLoading(false);
      }
    };

    loadRendleyUI();
  }, []);

  // Handle editor events
  useEffect(() => {
    if (!isLoaded || !editorRef.current) return;

    const editor = editorRef.current;

    const handleExport = (event: any) => {
      console.log('Video exported:', event.detail);
      // You can handle the exported video here
    };

    const handleSave = (event: any) => {
      console.log('Project saved:', event.detail);
      // You can handle the saved project here
    };

    const handleLoad = (event: any) => {
      console.log('Project loaded:', event.detail);
      // You can handle the loaded project here
    };

    // Add event listeners for editor events
    editor.addEventListener('export', handleExport);
    editor.addEventListener('save', handleSave);
    editor.addEventListener('load', handleLoad);

    return () => {
      editor.removeEventListener('export', handleExport);
      editor.removeEventListener('save', handleSave);
      editor.removeEventListener('load', handleLoad);
    };
  }, [isLoaded]);

  if (error) {
    return (
      <div className="w-full p-8 bg-red-50 border border-red-200 rounded-lg">
        <div className="text-center">
          <h3 className="text-red-600 font-semibold text-lg mb-2">❌ Failed to Load Video Editor</h3>
          <p className="text-red-500 text-sm mb-4">{error}</p>
          <div className="text-xs text-gray-600">
            <p>Possible solutions:</p>
            <ul className="list-disc list-inside mt-2 space-y-1">
              <li>Check your internet connection</li>
              <li>Verify your license credentials</li>
              <li>Try refreshing the page</li>
              <li>Check browser console for more details</li>
            </ul>
          </div>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="w-full p-8 bg-blue-50 border border-blue-200 rounded-lg">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <h3 className="text-blue-600 font-semibold text-lg mb-2">⏳ Loading Video Editor</h3>
          <p className="text-blue-500 text-sm">Please wait while we load the Rendley Video Editor UI...</p>
          <div className="text-xs text-gray-600 mt-4">
            <p>This may take a few moments on first load</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full">
      <div className="text-center mb-4">
        <h2 className="text-2xl font-bold mb-2">🎬 Rendley Video Editor UI</h2>
        <p className="text-gray-600">Full-featured video editor with built-in UI</p>
        <div className="inline-flex items-center px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm font-medium mt-2">
          ✅ Loaded Successfully
        </div>
      </div>

      {/* Rendley Video Editor Custom Element */}
      <div className="w-full border border-gray-300 rounded-lg overflow-hidden shadow-lg">
        <rendley-video-editor
          ref={editorRef}
          id="rendley-editor"
          licensename="mides"
          licensekey="54CB577408EA73C950FA0001"
          pexelsapikey={pexelsApiKey}
          giphyapikey={giphyApiKey}
          theme={theme}
        />
      </div>

      {/* Info Panel */}
      <div className="mt-4 p-4 bg-gray-50 rounded-lg">
        <h3 className="font-semibold mb-2">ℹ️ Editor Features</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
          <div>
            <h4 className="font-medium text-gray-800">✨ Built-in Features:</h4>
            <ul className="list-disc list-inside space-y-1 mt-1">
              <li>Drag & drop interface</li>
              <li>Timeline editing</li>
              <li>Text and graphics</li>
              <li>Transitions & effects</li>
              <li>Audio editing</li>
            </ul>
          </div>
          <div>
            <h4 className="font-medium text-gray-800">🔧 Configuration:</h4>
            <ul className="list-disc list-inside space-y-1 mt-1">
              <li>License: mides</li>
              <li>Theme: {theme}</li>
              <li>Pexels API: {pexelsApiKey ? '✅ Configured' : '❌ Not set'}</li>
              <li>Giphy API: {giphyApiKey ? '✅ Configured' : '❌ Not set'}</li>
            </ul>
          </div>
        </div>
      </div>

      {/* API Keys Info */}
      {(!pexelsApiKey || !giphyApiKey) && (
        <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <h3 className="font-semibold text-yellow-800 mb-2">🔑 Optional API Keys</h3>
          <p className="text-yellow-700 text-sm mb-2">
            Add these API keys to unlock additional features:
          </p>
          <ul className="text-sm text-yellow-600 space-y-1">
            {!pexelsApiKey && (
              <li>• <strong>Pexels API</strong>: Free stock photos and videos - Get at <a href="https://www.pexels.com/api/" target="_blank" rel="noopener noreferrer" className="underline">pexels.com/api</a></li>
            )}
            {!giphyApiKey && (
              <li>• <strong>Giphy API</strong>: GIF library access - Get at <a href="https://developers.giphy.com/" target="_blank" rel="noopener noreferrer" className="underline">developers.giphy.com</a></li>
            )}
          </ul>
        </div>
      )}
    </div>
  );
}
