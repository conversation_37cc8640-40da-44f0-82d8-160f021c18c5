'use client';

import { useState, useEffect } from 'react';
import CDNTroubleshootingGuide from './CDNTroubleshootingGuide';

export default function RendleyUIStatus() {
  const [cdnStatus, setCdnStatus] = useState<'checking' | 'available' | 'unavailable'>('checking');
  const [errorDetails, setErrorDetails] = useState<string>('');

  useEffect(() => {
    checkCDNStatus();
  }, []);

  const checkCDNStatus = async () => {
    setCdnStatus('checking');
    
    try {
      // Test 1: Basic connectivity
      console.log('Testing CDN connectivity...');
      
      // Try to fetch the CDN resource
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout
      
      try {
        const response = await fetch('https://cdn.rendley.com/sdk/video-editor/1.0.0/loader/index.js', {
          method: 'HEAD',
          signal: controller.signal,
          mode: 'no-cors' // Avoid CORS issues
        });
        
        clearTimeout(timeoutId);
        console.log('CDN connectivity test passed');
        
        // Test 2: Try module import
        try {
          const module = await import('https://cdn.rendley.com/sdk/video-editor/1.0.0/loader/index.js');
          if (module && module.defineCustomElements) {
            setCdnStatus('available');
            console.log('CDN is fully available');
          } else {
            setCdnStatus('unavailable');
            setErrorDetails('CDN accessible but module incomplete');
          }
        } catch (importError) {
          setCdnStatus('unavailable');
          setErrorDetails(`Module import failed: ${importError}`);
          console.error('Module import failed:', importError);
        }
        
      } catch (fetchError) {
        clearTimeout(timeoutId);
        setCdnStatus('unavailable');
        setErrorDetails(`Network error: ${fetchError}`);
        console.error('CDN fetch failed:', fetchError);
      }
      
    } catch (error) {
      setCdnStatus('unavailable');
      setErrorDetails(`General error: ${error}`);
      console.error('CDN test failed:', error);
    }
  };

  const handleRetryTest = () => {
    checkCDNStatus();
  };

  const scrollToAlternatives = () => {
    const easyEditor = document.getElementById('easy-video-editor');
    if (easyEditor) {
      easyEditor.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <div className="w-full p-6 bg-white border border-gray-300 rounded-lg shadow-lg">
      <div className="text-center mb-6">
        <h2 className="text-2xl font-bold mb-2">🎬 Rendley Video Editor Status</h2>
        <p className="text-gray-600">Professional video editing interface</p>
      </div>

      {/* Status Display */}
      {cdnStatus === 'checking' && (
        <div className="p-6 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-center justify-center mb-4">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mr-3"></div>
            <span className="text-blue-600 font-medium text-lg">Checking CDN Status...</span>
          </div>
          <p className="text-blue-500 text-sm text-center">
            Testing connectivity to Rendley servers...
          </p>
        </div>
      )}

      {cdnStatus === 'available' && (
        <div className="p-6 bg-green-50 border border-green-200 rounded-lg">
          <div className="text-center">
            <div className="text-green-600 text-4xl mb-3">✅</div>
            <h3 className="text-green-600 font-bold text-xl mb-2">CDN Available!</h3>
            <p className="text-green-500 mb-4">
              Rendley Video Editor UI should load successfully
            </p>
            <div className="bg-white p-4 rounded border border-green-200">
              <p className="text-sm text-gray-600 mb-2">
                <strong>Note:</strong> If the UI still doesn't load, try:
              </p>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Refreshing the page (Ctrl+F5)</li>
                <li>• Disabling ad blockers</li>
                <li>• Clearing browser cache</li>
                <li>• Using incognito mode</li>
              </ul>
            </div>
          </div>
        </div>
      )}

      {cdnStatus === 'unavailable' && (
        <CDNTroubleshootingGuide />
      )}

      {/* Alternative Editors Preview */}
      <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
          <h4 className="font-semibold text-blue-800 mb-2">🚀 Easy Video Editor</h4>
          <p className="text-sm text-blue-700 mb-3">
            ✅ Works offline • No CDN required • Full features
          </p>
          <ul className="text-xs text-blue-600 space-y-1">
            <li>• Upload videos and images</li>
            <li>• Add custom text and effects</li>
            <li>• Real-time preview</li>
            <li>• Export to MP4</li>
          </ul>
        </div>
        
        <div className="p-4 bg-purple-50 rounded-lg border border-purple-200">
          <h4 className="font-semibold text-purple-800 mb-2">⚡ Advanced Video Editor</h4>
          <p className="text-sm text-purple-700 mb-3">
            ✅ Professional features • Timeline editing • No CDN required
          </p>
          <ul className="text-xs text-purple-600 space-y-1">
            <li>• Timeline with layers</li>
            <li>• Advanced export options</li>
            <li>• Multiple video formats</li>
            <li>• Professional controls</li>
          </ul>
        </div>
      </div>

      {/* Network Info */}
      <div className="mt-4 p-3 bg-gray-50 rounded border text-xs text-gray-600">
        <h5 className="font-semibold mb-2">Network Information:</h5>
        <div className="grid grid-cols-2 gap-2">
          <div><strong>CDN:</strong> cdn.rendley.com</div>
          <div><strong>Online:</strong> {navigator.onLine ? '✅ Yes' : '❌ No'}</div>
          <div><strong>Protocol:</strong> HTTPS</div>
          <div><strong>Test Time:</strong> {new Date().toLocaleTimeString()}</div>
        </div>
      </div>
    </div>
  );
}
