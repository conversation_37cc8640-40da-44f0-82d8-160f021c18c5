'use client';

import { useEffect, useRef, useState } from 'react';
import RendleyEngineManager from '../lib/RendleyEngineManager';

export default function TestVideoEditor() {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const engineRef = useRef<any>(null);
  const [status, setStatus] = useState('Initializing...');
  const [error, setError] = useState<string | null>(null);
  const [isExporting, setIsExporting] = useState(false);

  useEffect(() => {
    const initEngine = async () => {
      try {
        if (typeof window === 'undefined') return;
        if (!canvasRef.current) return;

        setStatus('Loading Rendley SDK...');
        
        const engineManager = RendleyEngineManager.getInstance();
        const engine = await engineManager.initializeEngine(canvasRef.current);

        engineRef.current = engine;
        setStatus('✅ Ready');
        
        // Add test content
        setTimeout(() => addTestContent(engine), 500);
        
      } catch (err) {
        console.error('Init error:', err);
        setError(err instanceof Error ? err.message : 'Unknown error');
        setStatus('❌ Failed');
      }
    };

    initEngine();
  }, []);

  const addTestContent = async (engine: any) => {
    try {
      await engine.addLayer({
        type: 'text',
        text: 'Test Video Export',
        fontSize: 60,
        fontFamily: 'Arial',
        color: '#FFFFFF',
        x: 540,
        y: 960,
        duration: 3000,
      });
      console.log('Test content added');
    } catch (err) {
      console.log('Could not add test content:', err);
    }
  };

  const handleSimpleExport = async () => {
    if (!engineRef.current || isExporting) return;
    
    try {
      setIsExporting(true);
      setStatus('Exporting...');
      
      console.log('Starting simple export test...');
      
      // Try the most basic export
      const result = await engineRef.current.export();
      
      console.log('Export result:', result);
      
      if (result instanceof Blob) {
        downloadBlob(result, 'test-video.mp4');
        setStatus('✅ Export Success!');
      } else {
        throw new Error('Export did not return a blob');
      }
      
    } catch (err) {
      console.error('Export error:', err);
      setStatus('❌ Export Failed');
      setError(err instanceof Error ? err.message : 'Export failed');
    } finally {
      setIsExporting(false);
    }
  };

  const handleAdvancedExport = async () => {
    if (!engineRef.current || isExporting) return;
    
    try {
      setIsExporting(true);
      setStatus('Exporting with options...');
      
      console.log('Starting advanced export test...');
      
      const result = await engineRef.current.export({
        format: 'mp4',
        quality: 'medium',
        fps: 24,
      });
      
      console.log('Advanced export result:', result);
      
      if (result instanceof Blob) {
        downloadBlob(result, 'test-video-advanced.mp4');
        setStatus('✅ Advanced Export Success!');
      } else if (result && result.blob) {
        downloadBlob(result.blob, 'test-video-advanced.mp4');
        setStatus('✅ Advanced Export Success!');
      } else {
        throw new Error('Advanced export did not return expected format');
      }
      
    } catch (err) {
      console.error('Advanced export error:', err);
      setStatus('❌ Advanced Export Failed');
      setError(err instanceof Error ? err.message : 'Advanced export failed');
    } finally {
      setIsExporting(false);
    }
  };

  const downloadBlob = (blob: Blob, filename: string) => {
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    console.log(`Downloaded: ${filename}, size: ${blob.size} bytes`);
  };

  const handleReset = () => {
    setError(null);
    setStatus('Ready');
  };

  if (error) {
    return (
      <div className="p-6 bg-red-50 border border-red-200 rounded-lg">
        <h3 className="text-red-600 font-semibold">❌ Test Error</h3>
        <p className="text-red-500 text-sm mt-2">{error}</p>
        <button 
          onClick={handleReset}
          className="mt-3 px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
        >
          Reset Test
        </button>
      </div>
    );
  }

  return (
    <div className="p-6 bg-white rounded-lg shadow-lg border">
      <div className="text-center mb-4">
        <h2 className="text-xl font-bold mb-2">🧪 Export Test</h2>
        <p className="text-gray-600">Status: <span className="font-semibold">{status}</span></p>
      </div>

      <div className="flex flex-col items-center space-y-4">
        <div className="border-2 border-gray-300 rounded-lg overflow-hidden bg-gray-900">
          <canvas
            ref={canvasRef}
            width={1080}
            height={1920}
            className="block"
            style={{
              width: '200px',
              height: '356px',
            }}
          />
        </div>

        {status.includes('Ready') && (
          <div className="flex gap-3 flex-wrap justify-center">
            <button
              onClick={handleSimpleExport}
              disabled={isExporting}
              className="px-4 py-2 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-300 text-white rounded-lg font-medium transition-colors"
            >
              {isExporting ? '⏳ Exporting...' : '📥 Simple Export'}
            </button>
            
            <button
              onClick={handleAdvancedExport}
              disabled={isExporting}
              className="px-4 py-2 bg-purple-500 hover:bg-purple-600 disabled:bg-gray-300 text-white rounded-lg font-medium transition-colors"
            >
              {isExporting ? '⏳ Exporting...' : '🚀 Advanced Export'}
            </button>
          </div>
        )}

        <div className="text-center text-xs text-gray-500">
          <p>This component tests different export methods</p>
          <p>Check browser console for detailed logs</p>
        </div>
      </div>
    </div>
  );
}
