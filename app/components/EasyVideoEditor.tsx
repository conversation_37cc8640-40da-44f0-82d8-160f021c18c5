'use client';

import { useEffect, useRef, useState } from 'react';
import RendleyEngineManager from '../lib/RendleyEngineManager';

export default function EasyVideoEditor() {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const engineRef = useRef<any>(null);
  const imageInputRef = useRef<HTMLInputElement>(null);
  const videoInputRef = useRef<HTMLInputElement>(null);
  
  const [isInitialized, setIsInitialized] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [layers, setLayers] = useState<any[]>([]);
  const [isExporting, setIsExporting] = useState(false);
  const [showInstructions, setShowInstructions] = useState(true);

  useEffect(() => {
    const initEngine = async () => {
      try {
        if (typeof window === 'undefined') return;
        if (!canvasRef.current) return;

        const engineManager = RendleyEngineManager.getInstance();
        const engine = await engineManager.initializeEngine(canvasRef.current);

        engineRef.current = engine;
        setIsInitialized(true);

        // Add welcome content
        setTimeout(() => addWelcomeContent(engine), 500);

      } catch (err) {
        console.error('Init error:', err);
        setError(err instanceof Error ? err.message : 'Unknown error');
      }
    };

    initEngine();

    // Cleanup function
    return () => {
      // Don't cleanup engine here as it might be used by other components
    };
  }, []);

  const addWelcomeContent = async (engine: any) => {
    try {
      const welcomeLayer = await engine.addLayer({
        type: 'text',
        text: '🎬 Easy Video Editor',
        fontSize: 64,
        fontFamily: 'Arial',
        color: '#FFFFFF',
        x: 540,
        y: 400,
        duration: 10000,
      });

      const instructionLayer = await engine.addLayer({
        type: 'text',
        text: 'Click buttons below to add content',
        fontSize: 36,
        fontFamily: 'Arial',
        color: '#888888',
        x: 540,
        y: 500,
        duration: 10000,
      });

      setLayers([
        { name: 'Welcome Title', type: 'text' },
        { name: 'Instructions', type: 'text' }
      ]);
    } catch (err) {
      console.log('Could not add welcome content:', err);
    }
  };

  const handleAddText = async () => {
    if (!engineRef.current) return;
    
    const text = prompt('Enter your text:');
    if (!text) return;

    try {
      await engineRef.current.addLayer({
        type: 'text',
        text: text,
        fontSize: 48,
        fontFamily: 'Arial',
        color: '#FFFFFF',
        x: 540,
        y: Math.random() * 1000 + 400,
        duration: 5000,
      });
      
      setLayers(prev => [...prev, { name: `Text: ${text.substring(0, 20)}...`, type: 'text' }]);
      setShowInstructions(false);
    } catch (err) {
      alert('Error adding text: ' + (err instanceof Error ? err.message : 'Unknown error'));
    }
  };

  const handleAddImage = () => {
    imageInputRef.current?.click();
  };

  const handleAddVideo = () => {
    videoInputRef.current?.click();
  };

  const handleImageSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file || !engineRef.current) return;

    try {
      const fileUrl = URL.createObjectURL(file);
      
      await engineRef.current.addLayer({
        type: 'image',
        source: fileUrl,
        x: 0,
        y: 0,
        width: 1080,
        height: 1920,
        duration: 5000,
      });
      
      setLayers(prev => [...prev, { name: `Image: ${file.name}`, type: 'image' }]);
      setShowInstructions(false);
    } catch (err) {
      alert('Error adding image: ' + (err instanceof Error ? err.message : 'Unknown error'));
    }
  };

  const handleVideoSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file || !engineRef.current) return;

    try {
      const fileUrl = URL.createObjectURL(file);
      
      await engineRef.current.addLayer({
        type: 'video',
        source: fileUrl,
        x: 0,
        y: 0,
        width: 1080,
        height: 1920,
        duration: 10000,
      });
      
      setLayers(prev => [...prev, { name: `Video: ${file.name}`, type: 'video' }]);
      setShowInstructions(false);
    } catch (err) {
      alert('Error adding video: ' + (err instanceof Error ? err.message : 'Unknown error'));
    }
  };

  const handlePlay = async () => {
    if (!engineRef.current) return;
    
    try {
      if (isPlaying) {
        await engineRef.current.pause();
        setIsPlaying(false);
      } else {
        await engineRef.current.play();
        setIsPlaying(true);
      }
    } catch (err) {
      console.error('Playback error:', err);
    }
  };

  const handleExport = async () => {
    if (!engineRef.current || isExporting) return;

    if (layers.length <= 2) { // Only welcome content
      alert('Please add some content (text, images, or videos) before exporting!');
      return;
    }

    try {
      setIsExporting(true);

      // Pause playback before export
      if (isPlaying) {
        try {
          await engineRef.current.pause();
          setIsPlaying(false);
        } catch (pauseErr) {
          console.log('Could not pause:', pauseErr);
        }
      }

      console.log('Starting video export...');

      // Try different export methods based on Rendley SDK version
      let exportResult;
      try {
        // Method 1: Standard export
        exportResult = await engineRef.current.export({
          format: 'mp4',
          quality: 'high',
          fps: 30,
          width: 1080,
          height: 1920,
        });
      } catch (exportErr) {
        console.log('Standard export failed, trying alternative method:', exportErr);

        // Method 2: Alternative export method
        try {
          exportResult = await engineRef.current.render({
            format: 'mp4',
            quality: 'high',
          });
        } catch (renderErr) {
          console.log('Render method failed:', renderErr);
          throw new Error('All export methods failed. Please check your content and try again.');
        }
      }

      // Handle different return types
      let blob;
      if (exportResult instanceof Blob) {
        blob = exportResult;
      } else if (exportResult && exportResult.blob) {
        blob = exportResult.blob;
      } else if (exportResult && exportResult.url) {
        // If it returns a URL, fetch it as blob
        try {
          const response = await fetch(exportResult.url);
          blob = await response.blob();
        } catch (fetchErr) {
          throw new Error('Failed to download exported video');
        }
      } else if (exportResult && typeof exportResult === 'string') {
        // If it returns a data URL
        try {
          const response = await fetch(exportResult);
          blob = await response.blob();
        } catch (fetchErr) {
          throw new Error('Failed to process exported video');
        }
      } else {
        throw new Error('Export returned unexpected format');
      }

      // Validate blob
      if (!blob || blob.size === 0) {
        throw new Error('Export produced empty video file');
      }

      // Create download
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `easy-video-${Date.now()}.mp4`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      console.log('Video exported successfully, size:', blob.size, 'bytes');
      alert('🎉 Video exported successfully!');

    } catch (err) {
      console.error('Export error:', err);
      const errorMessage = err instanceof Error ? err.message : 'Unknown export error';
      alert(`Export failed: ${errorMessage}\n\nTips:\n- Make sure you have added content\n- Try refreshing the page and trying again`);
    } finally {
      setIsExporting(false);
    }
  };

  const handleClear = async () => {
    if (!engineRef.current) return;
    
    if (confirm('Clear all content and start over?')) {
      try {
        // Reset engine or clear layers
        setLayers([]);
        setShowInstructions(true);
        
        // Re-add welcome content
        setTimeout(() => addWelcomeContent(engineRef.current), 100);
      } catch (err) {
        console.error('Clear error:', err);
      }
    }
  };

  if (error) {
    return (
      <div className="p-6 bg-red-50 border border-red-200 rounded-lg">
        <h3 className="text-red-600 font-semibold">❌ Error</h3>
        <p className="text-red-500 text-sm mt-2">{error}</p>
      </div>
    );
  }

  return (
    <div className="w-full max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-lg">
      <div className="text-center mb-6">
        <h2 className="text-3xl font-bold mb-2">🎬 Easy Video Editor</h2>
        <p className="text-gray-600">
          {isInitialized ? 
            <span className="text-green-600 font-semibold">✅ Ready to create!</span> : 
            <span className="text-yellow-600 font-semibold">⏳ Loading...</span>
          }
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        
        {/* Canvas */}
        <div className="flex flex-col items-center">
          <div className="border-2 border-gray-300 rounded-lg overflow-hidden bg-gray-900 shadow-lg">
            <canvas
              ref={canvasRef}
              width={1080}
              height={1920}
              className="block"
              style={{
                width: '270px',
                height: '480px',
                maxWidth: '100%'
              }}
            />
          </div>
          
          {/* Playback Controls */}
          {isInitialized && (
            <div className="mt-4 flex gap-3">
              <button
                onClick={handlePlay}
                className={`px-6 py-2 rounded-lg font-medium transition-colors ${
                  isPlaying 
                    ? 'bg-red-500 hover:bg-red-600 text-white' 
                    : 'bg-green-500 hover:bg-green-600 text-white'
                }`}
              >
                {isPlaying ? '⏸️ Pause' : '▶️ Play'}
              </button>
            </div>
          )}
        </div>

        {/* Controls */}
        <div className="space-y-4">
          
          {/* Instructions */}
          {showInstructions && (
            <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
              <h3 className="font-semibold text-blue-800 mb-2">📋 How to use:</h3>
              <ol className="text-sm text-blue-700 space-y-1">
                <li>1. Click "Add Text" to add custom text</li>
                <li>2. Click "Add Image" to upload photos</li>
                <li>3. Click "Add Video" to upload video clips</li>
                <li>4. Click "Play" to preview your creation</li>
                <li>5. Click "Export" to download your video</li>
              </ol>
            </div>
          )}

          {/* Add Content Buttons */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="font-semibold mb-3">➕ Add Content</h3>
            <div className="grid grid-cols-1 gap-2">
              <button
                onClick={handleAddText}
                disabled={!isInitialized}
                className="px-4 py-3 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-300 text-white rounded-lg font-medium transition-colors"
              >
                📝 Add Text
              </button>
              
              <button
                onClick={handleAddImage}
                disabled={!isInitialized}
                className="px-4 py-3 bg-purple-500 hover:bg-purple-600 disabled:bg-gray-300 text-white rounded-lg font-medium transition-colors"
              >
                🖼️ Add Image
              </button>
              
              <button
                onClick={handleAddVideo}
                disabled={!isInitialized}
                className="px-4 py-3 bg-green-500 hover:bg-green-600 disabled:bg-gray-300 text-white rounded-lg font-medium transition-colors"
              >
                🎥 Add Video
              </button>
            </div>
          </div>

          {/* Content List */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="font-semibold mb-3">📋 Your Content ({layers.length})</h3>
            <div className="space-y-2 max-h-32 overflow-y-auto">
              {layers.map((layer, index) => (
                <div key={index} className="p-2 bg-white rounded border text-sm">
                  {layer.name}
                </div>
              ))}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="space-y-2">
            <button
              onClick={handleExport}
              disabled={!isInitialized || isExporting}
              className="w-full px-4 py-3 bg-orange-500 hover:bg-orange-600 disabled:bg-gray-300 text-white rounded-lg font-medium transition-colors"
            >
              {isExporting ? '⏳ Creating Video...' : '📥 Export Video'}
            </button>
            
            <button
              onClick={handleClear}
              disabled={!isInitialized}
              className="w-full px-4 py-2 bg-gray-500 hover:bg-gray-600 disabled:bg-gray-300 text-white rounded-lg font-medium transition-colors"
            >
              🗑️ Clear All
            </button>
          </div>
        </div>
      </div>

      {/* Hidden inputs */}
      <input
        ref={imageInputRef}
        type="file"
        accept="image/*"
        onChange={handleImageSelect}
        className="hidden"
      />
      <input
        ref={videoInputRef}
        type="file"
        accept="video/*"
        onChange={handleVideoSelect}
        className="hidden"
      />
    </div>
  );
}
