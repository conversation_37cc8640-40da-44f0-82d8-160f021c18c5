'use client';

import { useEffect, useRef, useState } from 'react';
import { Engine } from '@rendley/sdk';

interface RendleyVideoEditorProps {
  width?: number;
  height?: number;
  backgroundColor?: string;
}

export default function RendleyVideoEditor({
  width = 1080,
  height = 1920,
  backgroundColor = '#000000'
}: RendleyVideoEditorProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const engineRef = useRef<any>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const initializeEngine = async () => {
      try {
        // Check if license credentials are available
        const licenseName = process.env.NEXT_PUBLIC_RENDLEY_LICENSE_NAME;
        const licenseKey = process.env.NEXT_PUBLIC_RENDLEY_LICENSE_KEY;

        if (!licenseName || !licenseKey || 
            licenseName === 'YOUR_LICENSE_NAME' || 
            licenseKey === 'YOUR_LICENSE_KEY') {
          setError('Please configure your Rendley license in .env.local file');
          return;
        }

        if (!canvasRef.current) {
          setError('Canvas element not found');
          return;
        }

        // Initialize the Rendley Engine
        const engine = Engine.getInstance().init({
          license: {
            licenseName,
            licenseKey,
          },
          display: {
            width,
            height,
            backgroundColor,
            view: canvasRef.current,
          },
        });

        engineRef.current = engine;
        setIsInitialized(true);
        setError(null);

        console.log('Rendley Engine initialized successfully');
      } catch (err) {
        console.error('Failed to initialize Rendley Engine:', err);
        setError(`Failed to initialize Rendley Engine: ${err instanceof Error ? err.message : 'Unknown error'}`);
      }
    };

    initializeEngine();

    // Cleanup function
    return () => {
      if (engineRef.current) {
        try {
          // Add cleanup logic here if Rendley provides cleanup methods
          engineRef.current = null;
        } catch (err) {
          console.error('Error during cleanup:', err);
        }
      }
    };
  }, [width, height, backgroundColor]);

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center p-8 bg-red-50 border border-red-200 rounded-lg">
        <div className="text-red-600 font-semibold mb-2">Rendley Engine Error</div>
        <div className="text-red-500 text-sm text-center">{error}</div>
        {error.includes('license') && (
          <div className="mt-4 text-sm text-gray-600">
            <p>To fix this:</p>
            <ol className="list-decimal list-inside mt-2 space-y-1">
              <li>Visit <a href="https://app.rendley.com/" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">https://app.rendley.com/</a> to get your license</li>
              <li>Update the .env.local file with your license credentials</li>
              <li>Restart the development server</li>
            </ol>
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="flex flex-col items-center space-y-4">
      <div className="text-center">
        <h2 className="text-2xl font-bold mb-2">Rendley Video Editor</h2>
        <p className="text-gray-600">
          Status: {isInitialized ? 
            <span className="text-green-600 font-semibold">Initialized</span> : 
            <span className="text-yellow-600 font-semibold">Initializing...</span>
          }
        </p>
      </div>
      
      <div className="border border-gray-300 rounded-lg overflow-hidden shadow-lg">
        <canvas
          ref={canvasRef}
          width={width}
          height={height}
          className="block max-w-full h-auto"
          style={{
            maxWidth: '100%',
            height: 'auto',
            aspectRatio: `${width}/${height}`
          }}
        />
      </div>

      {isInitialized && (
        <div className="text-center text-sm text-gray-500">
          <p>Canvas dimensions: {width} × {height}</p>
          <p>The black canvas indicates successful initialization</p>
        </div>
      )}
    </div>
  );
}
