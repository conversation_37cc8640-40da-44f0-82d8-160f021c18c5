'use client';

import { useEffect, useRef, useState } from 'react';
import dynamic from 'next/dynamic';

interface RendleyVideoEditorProps {
  width?: number;
  height?: number;
  backgroundColor?: string;
}

export default function RendleyVideoEditor({
  width = 1080,
  height = 1920,
  backgroundColor = '#000000'
}: RendleyVideoEditorProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const engineRef = useRef<any>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);

  useEffect(() => {
    const initializeEngine = async () => {
      try {
        // Check if we're in browser environment
        if (typeof window === 'undefined') {
          return;
        }

        // Dynamic import of Rendley SDK
        const { Engine } = await import('@rendley/sdk');

        // Check if license credentials are available
        const licenseName = process.env.NEXT_PUBLIC_RENDLEY_LICENSE_NAME;
        const licenseKey = process.env.NEXT_PUBLIC_RENDLEY_LICENSE_KEY;

        if (!licenseName || !licenseKey ||
            licenseName === 'YOUR_LICENSE_NAME' ||
            licenseKey === 'YOUR_LICENSE_KEY') {
          setError('Please configure your Rendley license in .env.local file');
          return;
        }

        if (!canvasRef.current) {
          setError('Canvas element not found');
          return;
        }

        // Initialize the Rendley Engine
        const engine = Engine.getInstance().init({
          license: {
            licenseName,
            licenseKey,
          },
          display: {
            width,
            height,
            backgroundColor,
            view: canvasRef.current,
          },
        });

        engineRef.current = engine;
        setIsInitialized(true);
        setError(null);

        console.log('Rendley Engine initialized successfully');

        // Add some basic content to demonstrate the engine is working
        await addSampleContent(engine);

      } catch (err) {
        console.error('Failed to initialize Rendley Engine:', err);
        setError(`Failed to initialize Rendley Engine: ${err instanceof Error ? err.message : 'Unknown error'}`);
      }
    };

    initializeEngine();

    // Cleanup function
    return () => {
      if (engineRef.current) {
        try {
          // Add cleanup logic here if Rendley provides cleanup methods
          engineRef.current = null;
        } catch (err) {
          console.error('Error during cleanup:', err);
        }
      }
    };
  }, [width, height, backgroundColor]);

  const addSampleContent = async (engine: any) => {
    try {
      // Add a text layer as sample content
      const textLayer = await engine.addTextLayer({
        text: 'Welcome to Rendley!',
        fontSize: 60,
        fontFamily: 'Arial',
        color: '#FFFFFF',
        x: width / 2,
        y: height / 2,
        duration: 5000, // 5 seconds
      });

      console.log('Sample text layer added');
    } catch (err) {
      console.log('Could not add sample content:', err);
      // This is not a critical error, just log it
    }
  };

  const handlePlay = async () => {
    if (!engineRef.current) return;

    try {
      if (isPlaying) {
        await engineRef.current.pause();
        setIsPlaying(false);
      } else {
        await engineRef.current.play();
        setIsPlaying(true);
      }
    } catch (err) {
      console.error('Error controlling playback:', err);
    }
  };

  const handleAddText = async () => {
    if (!engineRef.current) return;

    try {
      const textLayer = await engineRef.current.addTextLayer({
        text: 'New Text Layer',
        fontSize: 40,
        fontFamily: 'Arial',
        color: '#FF6B6B',
        x: Math.random() * (width - 200) + 100,
        y: Math.random() * (height - 100) + 50,
        duration: 3000,
      });

      console.log('New text layer added');
    } catch (err) {
      console.error('Error adding text layer:', err);
    }
  };

  const handleExport = async () => {
    if (!engineRef.current) return;

    try {
      console.log('Starting export...');
      const exportResult = await engineRef.current.export({
        format: 'mp4',
        quality: 'high'
      });

      console.log('Export completed:', exportResult);
      // Handle the exported video here
    } catch (err) {
      console.error('Error exporting video:', err);
    }
  };

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center p-8 bg-red-50 border border-red-200 rounded-lg">
        <div className="text-red-600 font-semibold mb-2">Rendley Engine Error</div>
        <div className="text-red-500 text-sm text-center">{error}</div>
        {error.includes('license') && (
          <div className="mt-4 text-sm text-gray-600">
            <p>To fix this:</p>
            <ol className="list-decimal list-inside mt-2 space-y-1">
              <li>Visit <a href="https://app.rendley.com/" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">https://app.rendley.com/</a> to get your license</li>
              <li>Update the .env.local file with your license credentials</li>
              <li>Restart the development server</li>
            </ol>
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="flex flex-col items-center space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold mb-2">Rendley Video Editor</h2>
        <p className="text-gray-600">
          Status: {isInitialized ?
            <span className="text-green-600 font-semibold">✅ Ready</span> :
            <span className="text-yellow-600 font-semibold">⏳ Initializing...</span>
          }
        </p>
      </div>

      {/* Canvas Container */}
      <div className="border border-gray-300 rounded-lg overflow-hidden shadow-lg bg-gray-100">
        <canvas
          ref={canvasRef}
          width={width}
          height={height}
          className="block max-w-full h-auto"
          style={{
            maxWidth: '400px', // Limit display size for better UI
            height: 'auto',
            aspectRatio: `${width}/${height}`
          }}
        />
      </div>

      {/* Control Panel */}
      {isInitialized && (
        <div className="flex flex-col items-center space-y-4">
          <div className="text-center text-sm text-gray-500">
            <p>Canvas: {width} × {height} pixels</p>
            <p>License: mides</p>
          </div>

          {/* Video Controls */}
          <div className="flex gap-3 flex-wrap justify-center">
            <button
              onClick={handlePlay}
              className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                isPlaying
                  ? 'bg-red-500 hover:bg-red-600 text-white'
                  : 'bg-green-500 hover:bg-green-600 text-white'
              }`}
            >
              {isPlaying ? '⏸️ Pause' : '▶️ Play'}
            </button>

            <button
              onClick={handleAddText}
              className="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg font-medium transition-colors"
            >
              ➕ Add Text
            </button>

            <button
              onClick={handleExport}
              className="px-4 py-2 bg-purple-500 hover:bg-purple-600 text-white rounded-lg font-medium transition-colors"
            >
              📥 Export Video
            </button>
          </div>

          {/* Info Panel */}
          <div className="bg-gray-50 p-4 rounded-lg text-sm text-gray-600 max-w-md text-center">
            <p className="font-semibold mb-2">🎬 Video Editor Ready!</p>
            <p>• Click "Add Text" to add new text layers</p>
            <p>• Use "Play/Pause" to preview your video</p>
            <p>• Export when you're ready to download</p>
          </div>
        </div>
      )}
    </div>
  );
}
