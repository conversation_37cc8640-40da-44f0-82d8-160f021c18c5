'use client';

import { useEffect, useState } from 'react';

interface RendleyUILoaderProps {
  onLoaded?: () => void;
  onError?: (error: string) => void;
}

export default function RendleyUILoader({ onLoaded, onError }: RendleyUILoaderProps) {
  const [status, setStatus] = useState<'loading' | 'loaded' | 'error'>('loading');
  const [errorMessage, setErrorMessage] = useState<string>('');

  useEffect(() => {
    const loadRendleyUI = async () => {
      try {
        // Check if already loaded
        if (window.customElements && window.customElements.get('rendley-video-editor')) {
          console.log('Rendley Video Editor UI already loaded');
          setStatus('loaded');
          onLoaded?.();
          return;
        }

        console.log('Loading Rendley Video Editor UI...');

        // Strategy 1: Try direct script injection
        const tryDirectLoad = () => {
          return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.type = 'module';
            script.id = 'rendley-loader-direct';

            script.innerHTML = `
              (async () => {
                try {
                  console.log('Method 1: Direct import from CDN...');
                  const { defineCustomElements } = await import('https://cdn.rendley.com/sdk/video-editor/1.0.0/loader/index.js');

                  console.log('Defining custom elements...');
                  await defineCustomElements();

                  console.log('Rendley Video Editor UI loaded successfully (Method 1)');
                  window.dispatchEvent(new CustomEvent('rendley-ui-loaded'));
                } catch (error) {
                  console.error('Method 1 failed:', error);
                  window.dispatchEvent(new CustomEvent('rendley-ui-error', {
                    detail: {
                      message: 'Method 1 failed: ' + (error.message || 'Unknown error'),
                      error: error
                    }
                  }));
                }
              })();
            `;

            document.head.appendChild(script);

            // Timeout for this method
            setTimeout(() => {
              reject(new Error('Method 1 timeout'));
            }, 15000);
          });
        };

        // Strategy 2: Try alternative CDN or local fallback
        const tryAlternativeLoad = () => {
          return new Promise((resolve, reject) => {
            console.log('Method 2: Trying alternative loading...');

            // Create a simple fallback message
            const fallbackScript = document.createElement('script');
            fallbackScript.innerHTML = `
              console.log('Method 2: Creating fallback notification...');
              window.dispatchEvent(new CustomEvent('rendley-ui-fallback'));
            `;

            document.head.appendChild(fallbackScript);

            setTimeout(() => {
              reject(new Error('Method 2 not available'));
            }, 5000);
          });
        };

        // Set up event listeners
        const handleLoaded = () => {
          console.log('Rendley UI loaded event received');
          setStatus('loaded');
          onLoaded?.();
        };

        const handleError = (event: any) => {
          const error = event.detail?.message || 'Unknown error loading Rendley UI';
          console.error('Rendley UI error event:', error);
          // Don't set error immediately, try next method
        };

        const handleFallback = () => {
          console.log('Using fallback mode');
          setStatus('error');
          setErrorMessage('CDN loading failed. Using fallback mode.');
          onError?.('CDN loading failed. Please check your internet connection.');
        };

        // Add event listeners
        window.addEventListener('rendley-ui-loaded', handleLoaded);
        window.addEventListener('rendley-ui-error', handleError);
        window.addEventListener('rendley-ui-fallback', handleFallback);

        // Try loading strategies in sequence
        try {
          await tryDirectLoad();
        } catch (error1) {
          console.log('Method 1 failed, trying method 2...', error1);
          try {
            await tryAlternativeLoad();
          } catch (error2) {
            console.log('All methods failed, showing error');
            setStatus('error');
            setErrorMessage('Failed to load Rendley UI from CDN. Please check your internet connection and try refreshing the page.');
            onError?.('Failed to load Rendley UI. Please check your internet connection.');
          }
        }

        // Global timeout as final fallback
        const globalTimeout = setTimeout(() => {
          if (status === 'loading') {
            console.log('Global timeout reached');
            setStatus('error');
            setErrorMessage('Timeout: Rendley UI failed to load. This might be due to network issues or CDN unavailability.');
            onError?.('Timeout: Rendley UI failed to load within 30 seconds');
          }
        }, 30000);

        // Cleanup function
        return () => {
          window.removeEventListener('rendley-ui-loaded', handleLoaded);
          window.removeEventListener('rendley-ui-error', handleError);
          window.removeEventListener('rendley-ui-fallback', handleFallback);
          clearTimeout(globalTimeout);
        };

      } catch (error) {
        const errorMsg = error instanceof Error ? error.message : 'Failed to initialize Rendley UI loader';
        console.error('Rendley UI loader error:', errorMsg);
        setStatus('error');
        setErrorMessage(errorMsg);
        onError?.(errorMsg);
      }
    };

    loadRendleyUI();
  }, [onLoaded, onError, status]);

  if (status === 'loading') {
    return (
      <div className="flex flex-col items-center justify-center p-8 bg-blue-50 border border-blue-200 rounded-lg">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mb-4"></div>
        <h3 className="text-blue-600 font-semibold text-lg mb-2">⏳ Loading Rendley Video Editor UI</h3>
        <p className="text-blue-500 text-sm text-center">
          Downloading and initializing the video editor interface...
        </p>
        <div className="text-xs text-gray-600 mt-4 text-center">
          <p>This may take a few moments on first load</p>
          <p>Please ensure you have a stable internet connection</p>
        </div>
      </div>
    );
  }

  if (status === 'error') {
    return (
      <div className="flex flex-col items-center justify-center p-8 bg-red-50 border border-red-200 rounded-lg">
        <div className="text-red-600 text-4xl mb-4">❌</div>
        <h3 className="text-red-600 font-semibold text-lg mb-2">Failed to Load Video Editor UI</h3>
        <p className="text-red-500 text-sm text-center mb-4">{errorMessage}</p>
        
        <div className="text-xs text-gray-600 text-center">
          <p className="font-medium mb-2">Troubleshooting steps:</p>
          <ul className="list-disc list-inside space-y-1">
            <li>Check your internet connection</li>
            <li>Disable ad blockers or browser extensions</li>
            <li>Try refreshing the page</li>
            <li>Check browser console for detailed errors</li>
            <li>Ensure your license is valid</li>
          </ul>
        </div>
        
        <button 
          onClick={() => window.location.reload()}
          className="mt-4 px-4 py-2 bg-red-500 hover:bg-red-600 text-white rounded-lg font-medium transition-colors"
        >
          🔄 Refresh Page
        </button>
      </div>
    );
  }

  // Status is 'loaded'
  return (
    <div className="flex flex-col items-center justify-center p-4 bg-green-50 border border-green-200 rounded-lg">
      <div className="text-green-600 text-2xl mb-2">✅</div>
      <h3 className="text-green-600 font-semibold">Rendley Video Editor UI Loaded</h3>
      <p className="text-green-500 text-sm">Ready to use!</p>
    </div>
  );
}
