'use client';

import { useEffect, useRef, useState } from 'react';
import RendleyEngineManager from '../lib/RendleyEngineManager';

export default function SimpleVideoDemo() {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const engineRef = useRef<any>(null);
  const [status, setStatus] = useState('Initializing...');
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const initEngine = async () => {
      try {
        if (typeof window === 'undefined') return;

        setStatus('Loading Rendley SDK...');
        
        // Dynamic import
        const { Engine } = await import('@rendley/sdk');
        
        setStatus('Initializing engine...');
        
        if (!canvasRef.current) {
          throw new Error('Canvas not found');
        }

        // Use Engine Manager
        const engineManager = RendleyEngineManager.getInstance();
        const engine = await engineManager.initializeEngine(canvasRef.current);

        engineRef.current = engine;
        setStatus('✅ Engine Ready!');
        
        // Add welcome text
        setTimeout(async () => {
          try {
            await addWelcomeContent(engine);
          } catch (err) {
            console.log('Could not add content:', err);
          }
        }, 1000);

      } catch (err) {
        console.error('Init error:', err);
        setError(err instanceof Error ? err.message : 'Unknown error');
        setStatus('❌ Failed');
      }
    };

    initEngine();
  }, []);

  const addWelcomeContent = async (engine: any) => {
    try {
      // Add title text
      await engine.addLayer({
        type: 'text',
        text: 'Welcome to Rendley!',
        fontSize: 72,
        fontFamily: 'Arial',
        color: '#FFFFFF',
        x: 540,
        y: 400,
        duration: 5000,
        animation: {
          fadeIn: { duration: 1000 }
        }
      });

      // Add subtitle
      await engine.addLayer({
        type: 'text',
        text: 'Video Editor Ready',
        fontSize: 48,
        fontFamily: 'Arial',
        color: '#00FF88',
        x: 540,
        y: 500,
        duration: 5000,
        startTime: 500,
        animation: {
          fadeIn: { duration: 1000 }
        }
      });

      console.log('Welcome content added');
    } catch (err) {
      console.log('Content error:', err);
    }
  };

  const handlePlay = async () => {
    if (!engineRef.current) return;
    try {
      await engineRef.current.play();
    } catch (err) {
      console.error('Play error:', err);
    }
  };

  const handleAddText = async () => {
    if (!engineRef.current) return;
    try {
      await engineRef.current.addLayer({
        type: 'text',
        text: `Text ${Date.now()}`,
        fontSize: 40,
        fontFamily: 'Arial',
        color: '#FF6B6B',
        x: Math.random() * 800 + 140,
        y: Math.random() * 1600 + 160,
        duration: 3000,
      });
    } catch (err) {
      console.error('Add text error:', err);
    }
  };

  if (error) {
    return (
      <div className="p-6 bg-red-50 border border-red-200 rounded-lg">
        <h3 className="text-red-600 font-semibold">❌ Error</h3>
        <p className="text-red-500 text-sm mt-2">{error}</p>
      </div>
    );
  }

  return (
    <div className="p-6 bg-white rounded-lg shadow-lg">
      <div className="text-center mb-4">
        <h2 className="text-2xl font-bold mb-2">🎬 Rendley Demo</h2>
        <p className="text-gray-600">Status: <span className="font-semibold">{status}</span></p>
      </div>

      <div className="flex flex-col items-center space-y-4">
        <div className="border-2 border-gray-300 rounded-lg overflow-hidden bg-gray-900">
          <canvas
            ref={canvasRef}
            width={1080}
            height={1920}
            className="block"
            style={{
              width: '270px',
              height: '480px',
              maxWidth: '100%'
            }}
          />
        </div>

        {status.includes('Ready') && (
          <div className="flex gap-3">
            <button
              onClick={handlePlay}
              className="px-4 py-2 bg-green-500 hover:bg-green-600 text-white rounded-lg font-medium transition-colors"
            >
              ▶️ Play
            </button>
            <button
              onClick={handleAddText}
              className="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg font-medium transition-colors"
            >
              ➕ Add Text
            </button>
          </div>
        )}

        <div className="text-center text-sm text-gray-500">
          <p>License: mides</p>
          <p>Resolution: 1080×1920</p>
        </div>
      </div>
    </div>
  );
}
