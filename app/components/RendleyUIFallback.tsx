'use client';

import { useState } from 'react';

export default function RendleyUIFallback() {
  const [showDetails, setShowDetails] = useState(false);

  const handleRetry = () => {
    window.location.reload();
  };

  const handleUseAlternative = () => {
    // Scroll to alternative editors
    const easyEditor = document.getElementById('easy-video-editor');
    if (easyEditor) {
      easyEditor.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <div className="w-full p-8 bg-gradient-to-br from-red-50 to-orange-50 border border-red-200 rounded-lg">
      <div className="text-center">
        <div className="text-6xl mb-4">🚫</div>
        <h3 className="text-red-600 font-bold text-2xl mb-2">Rendley UI Failed to Load</h3>
        <p className="text-red-500 text-lg mb-6">
          The professional video editor interface couldn't be loaded from the CDN.
        </p>

        {/* Quick Actions */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
          <button
            onClick={handleRetry}
            className="px-6 py-3 bg-red-500 hover:bg-red-600 text-white rounded-lg font-medium transition-colors"
          >
            🔄 Retry Loading
          </button>
          
          <button
            onClick={handleUseAlternative}
            className="px-6 py-3 bg-blue-500 hover:bg-blue-600 text-white rounded-lg font-medium transition-colors"
          >
            🚀 Use Alternative Editor
          </button>
        </div>

        {/* Troubleshooting */}
        <div className="bg-white p-6 rounded-lg border border-red-200 text-left max-w-2xl mx-auto">
          <h4 className="font-bold text-gray-800 mb-4 text-center">🔧 Troubleshooting Steps</h4>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h5 className="font-semibold text-gray-700 mb-2">🌐 Network Issues:</h5>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Check internet connection</li>
                <li>• Disable VPN if active</li>
                <li>• Try different network</li>
                <li>• Check firewall settings</li>
              </ul>
            </div>
            
            <div>
              <h5 className="font-semibold text-gray-700 mb-2">🔒 Browser Issues:</h5>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Disable ad blockers</li>
                <li>• Clear browser cache</li>
                <li>• Try incognito mode</li>
                <li>• Update browser</li>
              </ul>
            </div>
          </div>

          <div className="mt-4 p-3 bg-blue-50 rounded border border-blue-200">
            <h5 className="font-semibold text-blue-800 mb-2">💡 Alternative Solutions:</h5>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>• Use the "Easy Video Editor" below (fully functional)</li>
              <li>• Try the "Advanced Video Editor" (SDK-based)</li>
              <li>• Both alternatives work offline and don't require CDN</li>
            </ul>
          </div>

          <button
            onClick={() => setShowDetails(!showDetails)}
            className="mt-4 w-full px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded font-medium transition-colors"
          >
            {showDetails ? '▲ Hide' : '▼ Show'} Technical Details
          </button>

          {showDetails && (
            <div className="mt-4 p-4 bg-gray-50 rounded border text-xs text-gray-600">
              <h6 className="font-semibold mb-2">Technical Information:</h6>
              <ul className="space-y-1">
                <li><strong>CDN URL:</strong> https://cdn.rendley.com/sdk/video-editor/1.0.0/</li>
                <li><strong>Error:</strong> Timeout after 30 seconds</li>
                <li><strong>Possible Causes:</strong> Network latency, CDN unavailability, browser restrictions</li>
                <li><strong>Browser:</strong> {navigator.userAgent}</li>
                <li><strong>Time:</strong> {new Date().toLocaleString()}</li>
              </ul>
              
              <div className="mt-3 p-2 bg-yellow-50 border border-yellow-200 rounded">
                <p className="text-yellow-800 text-xs">
                  <strong>Note:</strong> This is a CDN loading issue, not a license problem. 
                  Your Rendley license (mides) is valid and working with the alternative editors below.
                </p>
              </div>
            </div>
          )}
        </div>

        {/* Alternative Editors Preview */}
        <div className="mt-8 grid grid-cols-1 md:grid-cols-2 gap-4 max-w-4xl mx-auto">
          <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
            <h4 className="font-semibold text-blue-800 mb-2">🚀 Easy Video Editor</h4>
            <p className="text-sm text-blue-700 mb-3">
              User-friendly interface with drag & drop, text, images, and video support.
            </p>
            <ul className="text-xs text-blue-600 space-y-1">
              <li>✅ Upload videos and images</li>
              <li>✅ Add custom text</li>
              <li>✅ Real-time preview</li>
              <li>✅ Export to MP4</li>
            </ul>
          </div>
          
          <div className="p-4 bg-purple-50 rounded-lg border border-purple-200">
            <h4 className="font-semibold text-purple-800 mb-2">⚡ Advanced Video Editor</h4>
            <p className="text-sm text-purple-700 mb-3">
              Professional timeline editor with layers, effects, and advanced controls.
            </p>
            <ul className="text-xs text-purple-600 space-y-1">
              <li>✅ Timeline editing</li>
              <li>✅ Layer management</li>
              <li>✅ Multiple formats</li>
              <li>✅ Advanced export options</li>
            </ul>
          </div>
        </div>

        {/* Contact Support */}
        <div className="mt-8 p-4 bg-gray-50 rounded-lg border">
          <h4 className="font-semibold text-gray-800 mb-2">📞 Need Help?</h4>
          <p className="text-sm text-gray-600 mb-3">
            If you continue experiencing issues, here are some resources:
          </p>
          <div className="flex flex-wrap gap-2 justify-center">
            <a
              href="https://docs.rendley.com/"
              target="_blank"
              rel="noopener noreferrer"
              className="px-3 py-1 bg-blue-500 text-white text-xs rounded hover:bg-blue-600 transition-colors"
            >
              📚 Documentation
            </a>
            <a
              href="https://discord.gg/BwdeFFEVXR"
              target="_blank"
              rel="noopener noreferrer"
              className="px-3 py-1 bg-purple-500 text-white text-xs rounded hover:bg-purple-600 transition-colors"
            >
              💬 Discord Support
            </a>
            <a
              href="https://github.com/rendleyhq/rendley-sdk-issues/issues"
              target="_blank"
              rel="noopener noreferrer"
              className="px-3 py-1 bg-gray-500 text-white text-xs rounded hover:bg-gray-600 transition-colors"
            >
              🐛 Report Issue
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}
