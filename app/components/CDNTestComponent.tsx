'use client';

import { useEffect, useState } from 'react';

export default function CDNTestComponent() {
  const [cdnStatus, setCdnStatus] = useState<'testing' | 'available' | 'unavailable'>('testing');
  const [testResults, setTestResults] = useState<string[]>([]);
  const [showDetails, setShowDetails] = useState(false);

  useEffect(() => {
    testCDNAvailability();
  }, []);

  const addTestResult = (message: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const testCDNAvailability = async () => {
    setCdnStatus('testing');
    addTestResult('Starting CDN availability test...');

    try {
      // Test 1: Basic fetch to CDN
      addTestResult('Test 1: Checking CDN connectivity...');
      const response = await fetch('https://cdn.rendley.com/sdk/video-editor/1.0.0/loader/index.js', {
        method: 'HEAD',
        mode: 'no-cors'
      });
      addTestResult('Test 1: CDN responded (no-cors mode)');

      // Test 2: Try to load as module
      addTestResult('Test 2: Attempting module import...');
      try {
        const module = await import('https://cdn.rendley.com/sdk/video-editor/1.0.0/loader/index.js');
        addTestResult('Test 2: Module import successful');
        
        if (module.defineCustomElements) {
          addTestResult('Test 3: defineCustomElements function found');
          setCdnStatus('available');
          addTestResult('✅ CDN is available and functional');
        } else {
          addTestResult('Test 3: defineCustomElements function not found');
          setCdnStatus('unavailable');
        }
      } catch (importError) {
        addTestResult(`Test 2: Module import failed - ${importError}`);
        setCdnStatus('unavailable');
      }

    } catch (error) {
      addTestResult(`Test 1: CDN connectivity failed - ${error}`);
      setCdnStatus('unavailable');
    }
  };

  const handleRetryTest = () => {
    setTestResults([]);
    testCDNAvailability();
  };

  return (
    <div className="w-full p-6 bg-white border border-gray-300 rounded-lg shadow-lg">
      <div className="text-center mb-6">
        <h3 className="text-xl font-bold mb-2">🔍 CDN Connectivity Test</h3>
        <p className="text-gray-600">Testing Rendley CDN availability</p>
      </div>

      {/* Status Display */}
      <div className="mb-6">
        {cdnStatus === 'testing' && (
          <div className="flex items-center justify-center p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mr-3"></div>
            <span className="text-blue-600 font-medium">Testing CDN connectivity...</span>
          </div>
        )}

        {cdnStatus === 'available' && (
          <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-center justify-center">
              <span className="text-green-600 text-2xl mr-2">✅</span>
              <span className="text-green-600 font-medium">CDN is available and working!</span>
            </div>
            <p className="text-green-500 text-sm text-center mt-2">
              Rendley Video Editor UI should load successfully
            </p>
          </div>
        )}

        {cdnStatus === 'unavailable' && (
          <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center justify-center">
              <span className="text-red-600 text-2xl mr-2">❌</span>
              <span className="text-red-600 font-medium">CDN is not accessible</span>
            </div>
            <p className="text-red-500 text-sm text-center mt-2">
              This explains why Rendley UI failed to load
            </p>
          </div>
        )}
      </div>

      {/* Action Buttons */}
      <div className="flex gap-3 justify-center mb-6">
        <button
          onClick={handleRetryTest}
          disabled={cdnStatus === 'testing'}
          className="px-4 py-2 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-300 text-white rounded-lg font-medium transition-colors"
        >
          🔄 Retry Test
        </button>
        
        <button
          onClick={() => setShowDetails(!showDetails)}
          className="px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-lg font-medium transition-colors"
        >
          {showDetails ? '▲ Hide' : '▼ Show'} Details
        </button>
      </div>

      {/* Test Results */}
      {showDetails && (
        <div className="bg-gray-50 p-4 rounded-lg border">
          <h4 className="font-semibold text-gray-800 mb-3">Test Results:</h4>
          <div className="bg-black text-green-400 p-3 rounded font-mono text-xs max-h-40 overflow-y-auto">
            {testResults.length === 0 ? (
              <div className="text-gray-500">No test results yet...</div>
            ) : (
              testResults.map((result, index) => (
                <div key={index} className="mb-1">{result}</div>
              ))
            )}
          </div>
        </div>
      )}

      {/* Recommendations */}
      <div className="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
        <h4 className="font-semibold text-blue-800 mb-2">💡 Recommendations:</h4>
        
        {cdnStatus === 'available' && (
          <div className="text-sm text-blue-700">
            <p>✅ CDN is working fine. If Rendley UI still fails to load, try:</p>
            <ul className="list-disc list-inside mt-2 space-y-1">
              <li>Refreshing the page</li>
              <li>Clearing browser cache</li>
              <li>Disabling browser extensions</li>
            </ul>
          </div>
        )}

        {cdnStatus === 'unavailable' && (
          <div className="text-sm text-blue-700">
            <p>❌ CDN is not accessible. Possible solutions:</p>
            <ul className="list-disc list-inside mt-2 space-y-1">
              <li>Check your internet connection</li>
              <li>Try a different network (mobile hotspot)</li>
              <li>Disable VPN if active</li>
              <li>Use the alternative editors below</li>
              <li>Contact your network administrator</li>
            </ul>
          </div>
        )}

        {cdnStatus === 'testing' && (
          <div className="text-sm text-blue-700">
            <p>🔍 Testing CDN connectivity and module availability...</p>
          </div>
        )}
      </div>

      {/* Network Info */}
      <div className="mt-4 p-3 bg-gray-50 rounded border text-xs text-gray-600">
        <h5 className="font-semibold mb-2">Network Information:</h5>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
          <div><strong>CDN URL:</strong> cdn.rendley.com</div>
          <div><strong>Protocol:</strong> HTTPS</div>
          <div><strong>User Agent:</strong> {navigator.userAgent.substring(0, 50)}...</div>
          <div><strong>Online:</strong> {navigator.onLine ? '✅ Yes' : '❌ No'}</div>
        </div>
      </div>
    </div>
  );
}
