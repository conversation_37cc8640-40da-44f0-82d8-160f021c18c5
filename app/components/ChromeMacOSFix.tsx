'use client';

import { useState } from 'react';

export default function ChromeMacOSFix() {
  const [currentStep, setCurrentStep] = useState(0);
  const [showCommands, setShowCommands] = useState(false);

  const macOSChromeFixes = [
    {
      title: "🚀 SOLUSI TERCEPAT",
      description: "Langsung gunakan editor yang bekerja 100%",
      action: "Gunakan Alternative Editor",
      command: "Scroll ke bawah → Easy Video Editor",
      success: "100%"
    },
    {
      title: "🔒 Disable Chrome Extensions",
      description: "Extensions sering memblokir CDN di macOS",
      action: "Disable Extensions",
      command: "chrome://extensions/ → Turn off all extensions",
      success: "85%"
    },
    {
      title: "🧹 Clear Chrome Cache (macOS)",
      description: "Cache corruption umum terjadi di Chrome macOS",
      action: "Clear Cache",
      command: "Cmd + Shift + Delete → Clear all data",
      success: "75%"
    },
    {
      title: "🕵️ Chrome Incognito Mode",
      description: "Test tanpa extensions dan cache",
      action: "Open Incognito",
      command: "Cmd + Shift + N → Visit localhost:3000",
      success: "80%"
    },
    {
      title: "🌐 Chrome Network Settings",
      description: "Reset network settings di Chrome",
      action: "Reset Network",
      command: "chrome://settings/reset → Advanced → Reset",
      success: "70%"
    },
    {
      title: "🔧 macOS Network Reset",
      description: "Reset DNS dan network di macOS",
      action: "Reset macOS Network",
      command: "Terminal commands untuk flush DNS",
      success: "90%"
    }
  ];

  const scrollToAlternatives = () => {
    const easyEditor = document.getElementById('easy-video-editor');
    if (easyEditor) {
      easyEditor.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const handleStepAction = (stepIndex: number) => {
    setCurrentStep(stepIndex);
    
    switch (stepIndex) {
      case 0:
        scrollToAlternatives();
        break;
      case 1:
        window.open('chrome://extensions/', '_blank');
        alert('Di tab yang baru terbuka:\n\n1. Turn off semua extensions\n2. Terutama ad blockers (uBlock Origin, AdBlock)\n3. Kembali ke tab ini dan refresh (Cmd+R)');
        break;
      case 2:
        alert('Untuk clear cache di Chrome macOS:\n\n1. Tekan Cmd + Shift + Delete\n2. Pilih "All time" untuk time range\n3. Centang semua opsi\n4. Klik "Clear data"\n5. Refresh halaman ini (Cmd + R)');
        break;
      case 3:
        alert('Untuk buka Incognito mode:\n\n1. Tekan Cmd + Shift + N\n2. Di window incognito, kunjungi: localhost:3000\n3. Test apakah CDN loading berhasil\n4. Jika berhasil, masalah di extensions/cache');
        break;
      case 4:
        window.open('chrome://settings/reset', '_blank');
        alert('Di tab yang baru terbuka:\n\n1. Scroll ke bawah\n2. Klik "Advanced"\n3. Klik "Reset and clean up"\n4. Klik "Reset settings to original defaults"\n5. Confirm reset');
        break;
      case 5:
        setShowCommands(true);
        break;
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    alert('Command copied to clipboard!');
  };

  return (
    <div className="w-full p-6 bg-gradient-to-br from-blue-50 to-purple-50 border border-blue-300 rounded-lg shadow-lg">
      <div className="text-center mb-6">
        <div className="text-4xl mb-3">🍎</div>
        <h2 className="text-2xl font-bold text-blue-800 mb-2">Chrome macOS CDN Fix</h2>
        <p className="text-blue-600 mb-4">
          Solusi khusus untuk Chrome di macOS dengan CDN timeout
        </p>
        
        {/* Technical Info */}
        <div className="bg-white p-4 rounded border border-blue-200 text-left">
          <h3 className="font-semibold text-gray-800 mb-2">📋 Detected Configuration:</h3>
          <div className="text-sm text-gray-600 space-y-1">
            <div><strong>OS:</strong> macOS 10.15.7 (Catalina)</div>
            <div><strong>Browser:</strong> Chrome *********</div>
            <div><strong>Error:</strong> CDN Timeout (30 seconds)</div>
            <div><strong>CDN:</strong> cdn.rendley.com</div>
            <div><strong>License:</strong> ✅ Valid (mides)</div>
          </div>
        </div>
      </div>

      {/* Quick Fix */}
      <div className="bg-green-50 p-4 rounded-lg border border-green-300 mb-6">
        <h3 className="font-bold text-green-800 text-lg mb-2 text-center">
          ⚡ RECOMMENDED: Skip CDN Issues
        </h3>
        <p className="text-green-700 text-sm text-center mb-3">
          Daripada troubleshooting CDN, langsung gunakan editor yang 100% bekerja
        </p>
        <button
          onClick={scrollToAlternatives}
          className="w-full px-4 py-3 bg-green-500 hover:bg-green-600 text-white rounded-lg font-bold transition-colors"
        >
          🚀 USE WORKING EDITOR NOW
        </button>
      </div>

      {/* Troubleshooting Steps */}
      <div className="space-y-3 mb-6">
        <h3 className="font-bold text-lg text-gray-800 text-center">🔧 Chrome macOS Troubleshooting</h3>
        
        {macOSChromeFixes.map((fix, index) => (
          <div 
            key={index}
            className={`p-4 rounded-lg border-2 transition-all ${
              currentStep === index 
                ? 'border-blue-500 bg-blue-50' 
                : 'border-gray-200 bg-white'
            }`}
          >
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <h4 className="font-semibold text-gray-800">{fix.title}</h4>
                <p className="text-sm text-gray-600 mt-1">{fix.description}</p>
                <div className="flex items-center mt-2 space-x-4">
                  <span className="text-xs text-blue-600">{fix.command}</span>
                  <span className={`text-xs px-2 py-1 rounded ${
                    parseInt(fix.success) >= 85 ? 'bg-green-100 text-green-800' :
                    parseInt(fix.success) >= 75 ? 'bg-yellow-100 text-yellow-800' :
                    'bg-orange-100 text-orange-800'
                  }`}>
                    {fix.success} success rate
                  </span>
                </div>
              </div>
              <button
                onClick={() => handleStepAction(index)}
                className="ml-4 px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded font-medium transition-colors"
              >
                {fix.action}
              </button>
            </div>
          </div>
        ))}
      </div>

      {/* macOS Terminal Commands */}
      {showCommands && (
        <div className="bg-black text-green-400 p-4 rounded-lg font-mono text-sm mb-6">
          <h4 className="text-white font-bold mb-3">💻 macOS Terminal Commands:</h4>
          
          <div className="space-y-3">
            <div>
              <div className="text-yellow-400 mb-1"># Flush DNS Cache (macOS)</div>
              <div className="bg-gray-800 p-2 rounded flex justify-between items-center">
                <code>sudo dscacheutil -flushcache && sudo killall -HUP mDNSResponder</code>
                <button 
                  onClick={() => copyToClipboard('sudo dscacheutil -flushcache && sudo killall -HUP mDNSResponder')}
                  className="ml-2 px-2 py-1 bg-blue-600 text-white text-xs rounded"
                >
                  Copy
                </button>
              </div>
            </div>
            
            <div>
              <div className="text-yellow-400 mb-1"># Reset Network Settings</div>
              <div className="bg-gray-800 p-2 rounded flex justify-between items-center">
                <code>sudo networksetup -setdnsservers Wi-Fi ******* *******</code>
                <button 
                  onClick={() => copyToClipboard('sudo networksetup -setdnsservers Wi-Fi ******* *******')}
                  className="ml-2 px-2 py-1 bg-blue-600 text-white text-xs rounded"
                >
                  Copy
                </button>
              </div>
            </div>
            
            <div>
              <div className="text-yellow-400 mb-1"># Test CDN Connectivity</div>
              <div className="bg-gray-800 p-2 rounded flex justify-between items-center">
                <code>curl -I https://cdn.rendley.com/sdk/video-editor/1.0.0/loader/index.js</code>
                <button 
                  onClick={() => copyToClipboard('curl -I https://cdn.rendley.com/sdk/video-editor/1.0.0/loader/index.js')}
                  className="ml-2 px-2 py-1 bg-blue-600 text-white text-xs rounded"
                >
                  Copy
                </button>
              </div>
            </div>
          </div>
          
          <div className="text-gray-400 text-xs mt-3">
            💡 Tip: Copy commands dan paste di Terminal (Cmd+Space → Terminal)
          </div>
        </div>
      )}

      {/* Chrome-Specific Issues */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
          <h4 className="font-semibold text-yellow-800 mb-2">⚠️ Chrome macOS Issues</h4>
          <ul className="text-sm text-yellow-700 space-y-1">
            <li>• Chrome extensions sering conflict di macOS</li>
            <li>• Cache corruption lebih sering terjadi</li>
            <li>• Network isolation lebih ketat</li>
            <li>• CORS restrictions lebih strict</li>
          </ul>
        </div>
        
        <div className="bg-green-50 p-4 rounded-lg border border-green-200">
          <h4 className="font-semibold text-green-800 mb-2">✅ Alternative Solutions</h4>
          <ul className="text-sm text-green-700 space-y-1">
            <li>• Easy Video Editor: 100% offline</li>
            <li>• Advanced Video Editor: No CDN needed</li>
            <li>• Same Rendley license works</li>
            <li>• Better performance than CDN</li>
          </ul>
        </div>
      </div>

      {/* Success Indicators */}
      <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
        <h4 className="font-semibold text-blue-800 mb-2">🎯 How to Know if Fixed:</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-blue-700">
          <div>
            <h5 className="font-medium mb-1">✅ CDN Working:</h5>
            <ul className="space-y-1">
              <li>• Professional Video Editor loads</li>
              <li>• No timeout errors</li>
              <li>• Full UI with timeline appears</li>
            </ul>
          </div>
          <div>
            <h5 className="font-medium mb-1">🚀 Alternative Working:</h5>
            <ul className="space-y-1">
              <li>• Easy Video Editor loads instantly</li>
              <li>• Can upload and edit videos</li>
              <li>• Export functionality works</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Final CTA */}
      <div className="text-center mt-6">
        <p className="text-sm text-gray-600 mb-3">
          <strong>Recommendation:</strong> Use alternative editors for immediate video editing
        </p>
        <div className="flex gap-2 justify-center">
          <button
            onClick={scrollToAlternatives}
            className="px-4 py-2 bg-green-500 hover:bg-green-600 text-white rounded-lg font-medium transition-colors"
          >
            🎬 Start Editing Now
          </button>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg font-medium transition-colors"
          >
            🔄 Retry CDN
          </button>
        </div>
      </div>
    </div>
  );
}
