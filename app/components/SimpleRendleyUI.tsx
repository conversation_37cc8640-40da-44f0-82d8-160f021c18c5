'use client';

import { useState } from 'react';
import RendleyUILoader from './RendleyUILoader';

declare global {
  namespace JSX {
    interface IntrinsicElements {
      'rendley-video-editor': {
        id?: string;
        licensename?: string;
        licensekey?: string;
        pexelsapikey?: string;
        giphyapikey?: string;
        theme?: 'light' | 'dark';
        style?: React.CSSProperties;
      };
    }
  }
}

export default function SimpleRendleyUI() {
  const [isUILoaded, setIsUILoaded] = useState(false);
  const [loadError, setLoadError] = useState<string | null>(null);

  const handleUILoaded = () => {
    console.log('Rendley UI loaded successfully');
    setIsUILoaded(true);
    setLoadError(null);
  };

  const handleUIError = (error: string) => {
    console.error('Rendley UI load error:', error);
    setLoadError(error);
    setIsUILoaded(false);
  };

  return (
    <div className="w-full">

      {/* Show loader while loading */}
      {!isUILoaded && !loadError && (
        <RendleyUILoader 
          onLoaded={handleUILoaded}
          onError={handleUIError}
        />
      )}

      {/* Show error if loading failed */}
      {loadError && (
        <div className="p-6 bg-red-50 border border-red-200 rounded-lg">
          <h3 className="text-red-600 font-semibold text-lg mb-2">❌ Video Editor UI Failed to Load</h3>
          <p className="text-red-500 text-sm mb-4">{loadError}</p>
          
          <div className="bg-white p-4 rounded border">
            <h4 className="font-medium text-gray-800 mb-2">💡 Alternative Options:</h4>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Use the "Easy Video Editor" below (custom implementation)</li>
              <li>• Try the "Advanced Video Editor" (SDK-based)</li>
              <li>• Check your internet connection and refresh</li>
              <li>• Disable browser extensions that might block CDN</li>
            </ul>
          </div>
          
          <button 
            onClick={() => {
              setLoadError(null);
              window.location.reload();
            }}
            className="mt-4 px-4 py-2 bg-red-500 hover:bg-red-600 text-white rounded-lg font-medium transition-colors"
          >
            🔄 Try Again
          </button>
        </div>
      )}

      {/* Show the video editor when loaded */}
      {isUILoaded && (
        <div className="space-y-6">
          {/* Video Editor Container */}
          <div className="w-full border border-gray-300 rounded-lg overflow-hidden shadow-lg bg-white">
            <rendley-video-editor
              id="rendley-video-editor-ui"
              licensename="mides"
              licensekey="54CB577408EA73C950FA0001"
              pexelsapikey={process.env.NEXT_PUBLIC_PEXELS_API_KEY || ''}
              giphyapikey={process.env.NEXT_PUBLIC_GIPHY_API_KEY || ''}
              theme="dark"
              style={{
                width: '100%',
                minHeight: '600px',
                display: 'block'
              }}
            />
          </div>

          {/* Features Info */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="p-4 bg-blue-50 rounded-lg">
              <h3 className="font-semibold text-blue-800 mb-2">🎨 Creative Tools</h3>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• Drag & drop interface</li>
                <li>• Text and graphics</li>
                <li>• Transitions & effects</li>
                <li>• Color correction</li>
              </ul>
            </div>
            
            <div className="p-4 bg-green-50 rounded-lg">
              <h3 className="font-semibold text-green-800 mb-2">📁 Media Library</h3>
              <ul className="text-sm text-green-700 space-y-1">
                <li>• Upload your videos</li>
                <li>• Stock photos (Pexels)</li>
                <li>• GIF library (Giphy)</li>
                <li>• Audio tracks</li>
              </ul>
            </div>
            
            <div className="p-4 bg-purple-50 rounded-lg">
              <h3 className="font-semibold text-purple-800 mb-2">⚡ Export Options</h3>
              <ul className="text-sm text-purple-700 space-y-1">
                <li>• Multiple formats</li>
                <li>• HD quality</li>
                <li>• Custom resolutions</li>
                <li>• Direct download</li>
              </ul>
            </div>
          </div>

          {/* API Keys Status */}
          <div className="p-4 bg-gray-50 rounded-lg">
            <h3 className="font-semibold text-gray-800 mb-2">🔑 API Configuration</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium">Rendley License:</span>
                <span className="ml-2 text-green-600">✅ Active (mides)</span>
              </div>
              <div>
                <span className="font-medium">Theme:</span>
                <span className="ml-2 text-blue-600">🌙 Dark Mode</span>
              </div>
              <div>
                <span className="font-medium">Pexels API:</span>
                <span className="ml-2">
                  {process.env.NEXT_PUBLIC_PEXELS_API_KEY && process.env.NEXT_PUBLIC_PEXELS_API_KEY !== 'YOUR_PEXELS_API_KEY' 
                    ? <span className="text-green-600">✅ Configured</span>
                    : <span className="text-yellow-600">⚠️ Not configured</span>
                  }
                </span>
              </div>
              <div>
                <span className="font-medium">Giphy API:</span>
                <span className="ml-2">
                  {process.env.NEXT_PUBLIC_GIPHY_API_KEY && process.env.NEXT_PUBLIC_GIPHY_API_KEY !== 'YOUR_GIPHY_API_KEY'
                    ? <span className="text-green-600">✅ Configured</span>
                    : <span className="text-yellow-600">⚠️ Not configured</span>
                  }
                </span>
              </div>
            </div>
            
            {(!process.env.NEXT_PUBLIC_PEXELS_API_KEY || process.env.NEXT_PUBLIC_PEXELS_API_KEY === 'YOUR_PEXELS_API_KEY' ||
              !process.env.NEXT_PUBLIC_GIPHY_API_KEY || process.env.NEXT_PUBLIC_GIPHY_API_KEY === 'YOUR_GIPHY_API_KEY') && (
              <div className="mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded">
                <p className="text-yellow-800 text-sm">
                  <strong>Optional:</strong> Add API keys to .env.local for stock photos and GIFs:
                </p>
                <ul className="text-yellow-700 text-xs mt-1 space-y-1">
                  <li>• NEXT_PUBLIC_PEXELS_API_KEY - Free at <a href="https://www.pexels.com/api/" target="_blank" rel="noopener noreferrer" className="underline">pexels.com/api</a></li>
                  <li>• NEXT_PUBLIC_GIPHY_API_KEY - Free at <a href="https://developers.giphy.com/" target="_blank" rel="noopener noreferrer" className="underline">developers.giphy.com</a></li>
                </ul>
              </div>
            )}
          </div>

          {/* Usage Instructions */}
          <div className="p-4 bg-blue-50 rounded-lg">
            <h3 className="font-semibold text-blue-800 mb-2">📋 How to Use</h3>
            <ol className="text-sm text-blue-700 space-y-1">
              <li>1. <strong>Upload Media:</strong> Drag and drop videos, images, or audio files</li>
              <li>2. <strong>Edit Timeline:</strong> Arrange clips, add transitions and effects</li>
              <li>3. <strong>Add Text:</strong> Create titles, captions, and graphics</li>
              <li>4. <strong>Preview:</strong> Use the preview player to review your video</li>
              <li>5. <strong>Export:</strong> Choose format and quality, then download your video</li>
            </ol>
          </div>
        </div>
      )}
    </div>
  );
}
