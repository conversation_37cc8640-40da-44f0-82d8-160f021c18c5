'use client';

import { useState } from 'react';
import Rendley<PERSON>Loader from './RendleyUILoader';
import RendleyUIFallback from './RendleyUIFallback';

declare global {
  namespace JSX {
    interface IntrinsicElements {
      'rendley-video-editor': {
        id?: string;
        licensename?: string;
        licensekey?: string;
        pexelsapikey?: string;
        giphyapikey?: string;
        theme?: 'light' | 'dark';
        style?: React.CSSProperties;
      };
    }
  }
}

export default function SimpleRendleyUI() {
  const [isUILoaded, setIsUILoaded] = useState(false);
  const [loadError, setLoadError] = useState<string | null>(null);

  const handleUILoaded = () => {
    console.log('Rendley UI loaded successfully');
    setIsUILoaded(true);
    setLoadError(null);
  };

  const handleUIError = (error: string) => {
    console.error('Rendley UI load error:', error);
    setLoadError(error);
    setIsUILoaded(false);
  };

  return (
    <div className="w-full">

      {/* Show loader while loading */}
      {!isUILoaded && !loadError && (
        <RendleyUILoader
          onLoaded={handleUILoaded}
          onError={handleUIError}
        />
      )}

      {/* Show comprehensive fallback if loading failed */}
      {loadError && (
        <RendleyUIFallback />
      )}

      {/* Show the video editor when loaded */}
      {isUILoaded && (
        <div className="space-y-6">
          {/* Video Editor Container */}
          <div className="w-full border border-gray-300 rounded-lg overflow-hidden shadow-lg bg-white">
            <rendley-video-editor
              id="rendley-video-editor-ui"
              licensename="mides"
              licensekey="54CB577408EA73C950FA0001"
              pexelsapikey={process.env.NEXT_PUBLIC_PEXELS_API_KEY || ''}
              giphyapikey={process.env.NEXT_PUBLIC_GIPHY_API_KEY || ''}
              theme="dark"
              style={{
                width: '100%',
                minHeight: '600px',
                display: 'block'
              }}
            />
          </div>
        </div>
      )}
    </div>
  );
}
