'use client';

import { useState } from 'react';

export default function CDNTroubleshootingGuide() {
  const [currentStep, setCurrentStep] = useState(0);
  const [testResults, setTestResults] = useState<string[]>([]);

  const troubleshootingSteps = [
    {
      title: "🔍 Step 1: Quick Network Test",
      description: "Test your internet connection and CDN accessibility",
      action: "Run Network Test",
      solution: "This will check if the problem is network-related"
    },
    {
      title: "🔒 Step 2: Disable Ad Blockers",
      description: "Ad blockers often block CDN scripts",
      action: "Check Ad Blockers",
      solution: "Temporarily disable uBlock Origin, AdBlock Plus, etc."
    },
    {
      title: "🧹 Step 3: Clear Browser Cache",
      description: "Old cached files can cause loading issues",
      action: "Clear Cache",
      solution: "Press Ctrl+Shift+Delete (Windows) or Cmd+Shift+Delete (Mac)"
    },
    {
      title: "🕵️ Step 4: Try Incognito Mode",
      description: "Test without extensions and cached data",
      action: "Open Incognito",
      solution: "Press Ctrl+Shift+N (Windows) or Cmd+Shift+N (Mac)"
    },
    {
      title: "🌐 Step 5: Check Network Type",
      description: "Corporate/school networks often block CDNs",
      action: "Check Network",
      solution: "Try mobile hotspot or different network"
    },
    {
      title: "🚀 Step 6: Use Alternative Editors",
      description: "Skip CDN issues entirely with offline editors",
      action: "Use Alternatives",
      solution: "Scroll down to Easy Video Editor or Advanced Editor"
    }
  ];

  const runNetworkTest = async () => {
    setTestResults(['🔍 Starting network diagnostics...']);
    
    // Test 1: Basic connectivity
    try {
      setTestResults(prev => [...prev, '📡 Testing basic internet connectivity...']);
      const response = await fetch('https://www.google.com/favicon.ico', { 
        method: 'HEAD', 
        mode: 'no-cors',
        cache: 'no-cache'
      });
      setTestResults(prev => [...prev, '✅ Internet connection: OK']);
    } catch (error) {
      setTestResults(prev => [...prev, '❌ Internet connection: FAILED']);
      return;
    }

    // Test 2: CDN accessibility
    try {
      setTestResults(prev => [...prev, '🌐 Testing CDN accessibility...']);
      const cdnResponse = await fetch('https://cdn.rendley.com/sdk/video-editor/1.0.0/loader/index.js', {
        method: 'HEAD',
        mode: 'no-cors',
        cache: 'no-cache'
      });
      setTestResults(prev => [...prev, '✅ CDN accessibility: OK']);
    } catch (error) {
      setTestResults(prev => [...prev, '❌ CDN accessibility: BLOCKED']);
      setTestResults(prev => [...prev, '💡 Solution: Try different network or use alternative editors']);
      return;
    }

    // Test 3: Module loading
    try {
      setTestResults(prev => [...prev, '📦 Testing module loading...']);
      const module = await import('https://cdn.rendley.com/sdk/video-editor/1.0.0/loader/index.js');
      if (module.defineCustomElements) {
        setTestResults(prev => [...prev, '✅ Module loading: OK']);
        setTestResults(prev => [...prev, '🎉 CDN should work! Try refreshing the page.']);
      } else {
        setTestResults(prev => [...prev, '❌ Module loading: INCOMPLETE']);
      }
    } catch (error) {
      setTestResults(prev => [...prev, '❌ Module loading: FAILED']);
      setTestResults(prev => [...prev, `📝 Error: ${error}`]);
      setTestResults(prev => [...prev, '💡 Solution: Use alternative editors below']);
    }
  };

  const scrollToAlternatives = () => {
    const easyEditor = document.getElementById('easy-video-editor');
    if (easyEditor) {
      easyEditor.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const handleStepAction = (stepIndex: number) => {
    switch (stepIndex) {
      case 0:
        runNetworkTest();
        break;
      case 1:
        alert('Please disable your ad blocker extensions:\n\n1. Click the extension icon in your browser\n2. Disable for this site\n3. Refresh the page\n\nCommon ad blockers: uBlock Origin, AdBlock Plus, Ghostery');
        break;
      case 2:
        alert('To clear browser cache:\n\nChrome/Edge: Ctrl+Shift+Delete\nFirefox: Ctrl+Shift+Delete\nSafari: Cmd+Option+E\n\n1. Select "Cached images and files"\n2. Click "Clear data"\n3. Refresh this page');
        break;
      case 3:
        alert('To open incognito/private mode:\n\nChrome: Ctrl+Shift+N\nFirefox: Ctrl+Shift+P\nSafari: Cmd+Shift+N\nEdge: Ctrl+Shift+N\n\nThen visit this page again in the private window.');
        break;
      case 4:
        alert('Network troubleshooting:\n\n1. Try mobile hotspot\n2. Use different WiFi\n3. Contact IT department (corporate networks)\n4. Check if VPN is blocking CDNs\n5. Try from home network');
        break;
      case 5:
        scrollToAlternatives();
        break;
    }
    setCurrentStep(stepIndex);
  };

  return (
    <div className="w-full p-6 bg-white border border-red-300 rounded-lg shadow-lg">
      <div className="text-center mb-6">
        <div className="text-4xl mb-3">🚨</div>
        <h2 className="text-2xl font-bold text-red-600 mb-2">CDN Loading Failed</h2>
        <p className="text-red-500 mb-4">
          The professional video editor interface couldn't be loaded from the CDN.
        </p>
        <div className="bg-red-50 p-3 rounded border border-red-200">
          <p className="text-sm text-red-700">
            <strong>Don't worry!</strong> We have multiple solutions to get you editing videos right away.
          </p>
        </div>
      </div>

      {/* Quick Fix Button */}
      <div className="text-center mb-8">
        <button
          onClick={scrollToAlternatives}
          className="px-6 py-3 bg-green-500 hover:bg-green-600 text-white rounded-lg font-bold text-lg transition-colors shadow-lg"
        >
          🚀 SKIP TO WORKING EDITOR
        </button>
        <p className="text-sm text-gray-600 mt-2">
          Or follow the troubleshooting steps below
        </p>
      </div>

      {/* Troubleshooting Steps */}
      <div className="space-y-4 mb-6">
        <h3 className="font-bold text-lg text-gray-800 text-center">🔧 Troubleshooting Steps</h3>
        
        {troubleshootingSteps.map((step, index) => (
          <div 
            key={index}
            className={`p-4 rounded-lg border-2 transition-all ${
              currentStep === index 
                ? 'border-blue-500 bg-blue-50' 
                : 'border-gray-200 bg-gray-50'
            }`}
          >
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <h4 className="font-semibold text-gray-800">{step.title}</h4>
                <p className="text-sm text-gray-600 mt-1">{step.description}</p>
                <p className="text-xs text-blue-600 mt-2">{step.solution}</p>
              </div>
              <button
                onClick={() => handleStepAction(index)}
                className="ml-4 px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded font-medium transition-colors"
              >
                {step.action}
              </button>
            </div>
          </div>
        ))}
      </div>

      {/* Test Results */}
      {testResults.length > 0 && (
        <div className="bg-black text-green-400 p-4 rounded-lg font-mono text-sm mb-6">
          <h4 className="text-white font-bold mb-2">🔍 Diagnostic Results:</h4>
          <div className="max-h-40 overflow-y-auto">
            {testResults.map((result, index) => (
              <div key={index} className="mb-1">{result}</div>
            ))}
          </div>
        </div>
      )}

      {/* Common Solutions */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
          <h4 className="font-semibold text-yellow-800 mb-2">🏢 Corporate/School Networks</h4>
          <ul className="text-sm text-yellow-700 space-y-1">
            <li>• Firewalls often block CDNs</li>
            <li>• Contact IT department</li>
            <li>• Use mobile hotspot</li>
            <li>• Try alternative editors</li>
          </ul>
        </div>
        
        <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
          <h4 className="font-semibold text-blue-800 mb-2">🌍 Geographic Issues</h4>
          <ul className="text-sm text-blue-700 space-y-1">
            <li>• CDN may not be available in your region</li>
            <li>• Try VPN to different location</li>
            <li>• Use alternative editors (recommended)</li>
            <li>• Contact your ISP</li>
          </ul>
        </div>
      </div>

      {/* Alternative Solutions */}
      <div className="bg-green-50 p-6 rounded-lg border border-green-200">
        <h3 className="font-bold text-green-800 text-lg mb-4 text-center">
          ✅ GUARANTEED WORKING SOLUTIONS
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="bg-white p-4 rounded border border-green-300">
            <h4 className="font-semibold text-green-800 mb-2">🎬 Easy Video Editor</h4>
            <ul className="text-sm text-green-700 space-y-1 mb-3">
              <li>✅ Works 100% offline</li>
              <li>✅ No CDN required</li>
              <li>✅ Upload videos & images</li>
              <li>✅ Add text & effects</li>
              <li>✅ Export to MP4</li>
            </ul>
            <button
              onClick={scrollToAlternatives}
              className="w-full px-3 py-2 bg-green-500 hover:bg-green-600 text-white rounded font-medium transition-colors"
            >
              Use Easy Editor
            </button>
          </div>
          
          <div className="bg-white p-4 rounded border border-green-300">
            <h4 className="font-semibold text-green-800 mb-2">⚡ Advanced Video Editor</h4>
            <ul className="text-sm text-green-700 space-y-1 mb-3">
              <li>✅ Professional timeline</li>
              <li>✅ Layer management</li>
              <li>✅ Advanced export options</li>
              <li>✅ Multiple formats</li>
              <li>✅ No network required</li>
            </ul>
            <button
              onClick={scrollToAlternatives}
              className="w-full px-3 py-2 bg-purple-500 hover:bg-purple-600 text-white rounded font-medium transition-colors"
            >
              Use Advanced Editor
            </button>
          </div>
        </div>
        
        <div className="text-center mt-4">
          <p className="text-sm text-green-700">
            <strong>Both editors use the same Rendley license (mides) and provide full video editing capabilities!</strong>
          </p>
        </div>
      </div>
    </div>
  );
}
