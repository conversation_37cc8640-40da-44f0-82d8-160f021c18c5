'use client';

import { useEffect, useRef, useState } from 'react';
import RendleyEngineManager from '../lib/RendleyEngineManager';

export default function AdvancedVideoEditor() {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const engineRef = useRef<any>(null);
  const imageInputRef = useRef<HTMLInputElement>(null);
  const videoInputRef = useRef<HTMLInputElement>(null);

  const [isInitialized, setIsInitialized] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [layers, setLayers] = useState<any[]>([]);
  const [isExporting, setIsExporting] = useState(false);

  useEffect(() => {
    const initializeEngine = async () => {
      try {
        // Check if we're in browser environment
        if (typeof window === 'undefined') {
          return;
        }

        if (!canvasRef.current) {
          setError('Canvas element not found');
          return;
        }

        // Use Engine Manager to get initialized engine
        const engineManager = RendleyEngineManager.getInstance();
        const engine = await engineManager.initializeEngine(canvasRef.current);

        engineRef.current = engine;
        setIsInitialized(true);
        setError(null);

        // Set up event listeners if available
        try {
          engine.on('timeupdate', (time: number) => {
            setCurrentTime(time);
          });

          engine.on('durationchange', (dur: number) => {
            setDuration(dur);
          });
        } catch (eventErr) {
          console.log('Event listeners not available:', eventErr);
        }

        console.log('Advanced Rendley Engine ready');

      } catch (err) {
        console.error('Failed to initialize:', err);
        setError(`Initialization failed: ${err instanceof Error ? err.message : 'Unknown error'}`);
      }
    };

    initializeEngine();

    return () => {
      if (engineRef.current) {
        engineRef.current = null;
      }
    };
  }, []);

  const handlePlay = async () => {
    if (!engineRef.current) return;
    
    try {
      if (isPlaying) {
        await engineRef.current.pause();
        setIsPlaying(false);
      } else {
        await engineRef.current.play();
        setIsPlaying(true);
      }
    } catch (err) {
      console.error('Playback error:', err);
    }
  };

  const handleAddText = async () => {
    if (!engineRef.current) return;
    
    try {
      const textLayer = await engineRef.current.addLayer({
        type: 'text',
        text: 'Sample Text',
        fontSize: 48,
        fontFamily: 'Arial',
        color: '#FFFFFF',
        x: 540, // center
        y: 960, // center
        duration: 5000,
        startTime: currentTime,
      });
      
      setLayers(prev => [...prev, textLayer]);
      console.log('Text layer added');
    } catch (err) {
      console.error('Error adding text:', err);
    }
  };

  const handleAddImage = async () => {
    if (!imageInputRef.current) return;
    imageInputRef.current.click();
  };

  const handleAddVideo = async () => {
    if (!videoInputRef.current) return;
    videoInputRef.current.click();
  };

  const handleImageSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file || !engineRef.current) return;

    try {
      // Create object URL for the file
      const fileUrl = URL.createObjectURL(file);

      const imageLayer = await engineRef.current.addLayer({
        type: 'image',
        source: fileUrl,
        x: 0,
        y: 0,
        width: 1080,
        height: 1920,
        duration: 5000,
        startTime: currentTime,
      });

      setLayers(prev => [...prev, { ...imageLayer, name: `Image: ${file.name}` }]);
      console.log('Image layer added:', file.name);
    } catch (err) {
      console.error('Error adding image:', err);
      alert('Error adding image: ' + (err instanceof Error ? err.message : 'Unknown error'));
    }
  };

  const handleVideoSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file || !engineRef.current) return;

    try {
      // Create object URL for the video file
      const fileUrl = URL.createObjectURL(file);

      const videoLayer = await engineRef.current.addLayer({
        type: 'video',
        source: fileUrl,
        x: 0,
        y: 0,
        width: 1080,
        height: 1920,
        duration: 10000, // 10 seconds, will be adjusted based on video duration
        startTime: currentTime,
      });

      setLayers(prev => [...prev, { ...videoLayer, name: `Video: ${file.name}` }]);
      console.log('Video layer added:', file.name);
    } catch (err) {
      console.error('Error adding video:', err);
      alert('Error adding video: ' + (err instanceof Error ? err.message : 'Unknown error'));
    }
  };

  const handleSeek = (time: number) => {
    if (!engineRef.current) return;
    
    try {
      engineRef.current.seek(time);
      setCurrentTime(time);
    } catch (err) {
      console.error('Seek error:', err);
    }
  };

  const handleExport = async () => {
    if (!engineRef.current || isExporting) return;

    try {
      setIsExporting(true);
      console.log('Starting export...');

      // Pause playback before export
      if (isPlaying) {
        await engineRef.current.pause();
        setIsPlaying(false);
      }

      // Export with proper configuration
      const exportResult = await engineRef.current.export({
        format: 'mp4',
        quality: 'high',
        fps: 30,
        width: 1080,
        height: 1920,
      });

      // Handle different return types
      let blob;
      if (exportResult instanceof Blob) {
        blob = exportResult;
      } else if (exportResult.blob) {
        blob = exportResult.blob;
      } else if (exportResult.url) {
        // If it returns a URL, fetch it as blob
        const response = await fetch(exportResult.url);
        blob = await response.blob();
      } else {
        throw new Error('Unknown export result format');
      }

      // Create download link
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `rendley-video-${Date.now()}.mp4`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      console.log('Export completed and downloaded');
      alert('Video exported successfully!');

    } catch (err) {
      console.error('Export error:', err);
      alert('Export failed: ' + (err instanceof Error ? err.message : 'Unknown error'));
    } finally {
      setIsExporting(false);
    }
  };

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60000);
    const seconds = Math.floor((time % 60000) / 1000);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center p-8 bg-red-50 border border-red-200 rounded-lg">
        <div className="text-red-600 font-semibold mb-2">❌ Engine Error</div>
        <div className="text-red-500 text-sm text-center">{error}</div>
      </div>
    );
  }

  return (
    <div className="w-full max-w-6xl mx-auto p-6 bg-white rounded-lg shadow-lg">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        
        {/* Canvas Area */}
        <div className="lg:col-span-2">
          <div className="text-center mb-4">
            <h2 className="text-2xl font-bold mb-2">🎬 Advanced Video Editor</h2>
            <p className="text-gray-600">
              Status: {isInitialized ? 
                <span className="text-green-600 font-semibold">✅ Ready</span> : 
                <span className="text-yellow-600 font-semibold">⏳ Loading...</span>
              }
            </p>
          </div>
          
          <div className="border border-gray-300 rounded-lg overflow-hidden shadow-md bg-gray-900">
            <canvas
              ref={canvasRef}
              width={1080}
              height={1920}
              className="block w-full h-auto max-h-96"
              style={{ aspectRatio: '9/16' }}
            />
          </div>

          {/* Timeline Controls */}
          {isInitialized && (
            <div className="mt-4 p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm text-gray-600">{formatTime(currentTime)}</span>
                <span className="text-sm text-gray-600">{formatTime(duration)}</span>
              </div>
              
              <input
                type="range"
                min={0}
                max={duration}
                value={currentTime}
                onChange={(e) => handleSeek(Number(e.target.value))}
                className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
              />
              
              <div className="flex justify-center gap-3 mt-4">
                <button
                  onClick={handlePlay}
                  className={`px-6 py-2 rounded-lg font-medium transition-colors ${
                    isPlaying 
                      ? 'bg-red-500 hover:bg-red-600 text-white' 
                      : 'bg-green-500 hover:bg-green-600 text-white'
                  }`}
                >
                  {isPlaying ? '⏸️ Pause' : '▶️ Play'}
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Control Panel */}
        <div className="space-y-6">
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="font-semibold mb-3">🛠️ Add Content</h3>
            <div className="space-y-2">
              <button
                onClick={handleAddText}
                disabled={!isInitialized}
                className="w-full px-4 py-2 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-300 text-white rounded-lg font-medium transition-colors"
              >
                📝 Add Text
              </button>

              <button
                onClick={handleAddImage}
                disabled={!isInitialized}
                className="w-full px-4 py-2 bg-purple-500 hover:bg-purple-600 disabled:bg-gray-300 text-white rounded-lg font-medium transition-colors"
              >
                🖼️ Add Image
              </button>

              <button
                onClick={handleAddVideo}
                disabled={!isInitialized}
                className="w-full px-4 py-2 bg-green-500 hover:bg-green-600 disabled:bg-gray-300 text-white rounded-lg font-medium transition-colors"
              >
                🎥 Add Video
              </button>
            </div>
          </div>

          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="font-semibold mb-3">📤 Export</h3>
            <button
              onClick={handleExport}
              disabled={!isInitialized || isExporting || layers.length === 0}
              className="w-full px-4 py-2 bg-orange-500 hover:bg-orange-600 disabled:bg-gray-300 text-white rounded-lg font-medium transition-colors"
            >
              {isExporting ? '⏳ Exporting...' : '📥 Export MP4'}
            </button>
            {layers.length === 0 && (
              <p className="text-xs text-gray-500 mt-2">Add content first to export</p>
            )}
          </div>

          {/* Layers Panel */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="font-semibold mb-3">📋 Layers ({layers.length})</h3>
            <div className="space-y-2 max-h-40 overflow-y-auto">
              {layers.length === 0 ? (
                <p className="text-gray-500 text-sm">No content yet. Add text, images, or videos above.</p>
              ) : (
                layers.map((layer, index) => (
                  <div key={index} className="p-2 bg-white rounded border text-sm">
                    <div className="font-medium">{layer.name || `Layer ${index + 1}`}</div>
                    <div className="text-gray-500 text-xs">Type: {layer.type || 'Unknown'}</div>
                  </div>
                ))
              )}
            </div>
          </div>

          {/* Info Panel */}
          <div className="bg-blue-50 p-4 rounded-lg">
            <h3 className="font-semibold mb-2">ℹ️ Info</h3>
            <div className="text-sm text-gray-600 space-y-1">
              <p>Resolution: 1080×1920</p>
              <p>License: mides</p>
              <p>Format: MP4</p>
            </div>
          </div>
        </div>
      </div>

      {/* Hidden file inputs */}
      <input
        ref={imageInputRef}
        type="file"
        accept="image/*"
        onChange={handleImageSelect}
        className="hidden"
      />
      <input
        ref={videoInputRef}
        type="file"
        accept="video/*"
        onChange={handleVideoSelect}
        className="hidden"
      />
    </div>
  );
}
