# Next.js + Rendley Video Editor

This is a [Next.js](https://nextjs.org) project integrated with [Rendley SDK](https://docs.rendley.com/) for video editing capabilities.

## Features

- ✨ **Rendley SDK Integration** - Professional video editing in the browser
- 🎬 **Canvas-based Video Editor** - Real-time video editing and preview
- 🔧 **TypeScript Support** - Full type safety and IntelliSense
- 🎨 **Tailwind CSS** - Modern styling and responsive design
- ⚡ **Next.js 15** - Latest Next.js features and optimizations

## Prerequisites

Before you begin, you need to obtain a Rendley license:

1. Visit [https://app.rendley.com/](https://app.rendley.com/)
2. Sign up for an account
3. Get your license name and license key

## Getting Started

### 1. Clone and Install Dependencies

```bash
# Install dependencies
npm install
```

### 2. Configure Rendley License

Create a `.env.local` file in the root directory and add your Rendley credentials:

```env
# Rendley SDK License Configuration
NEXT_PUBLIC_RENDLEY_LICENSE_NAME=your_actual_license_name
NEXT_PUBLIC_RENDLEY_LICENSE_KEY=your_actual_license_key
```

**Important:** Replace `your_actual_license_name` and `your_actual_license_key` with your real Rendley license credentials.

### 3. Run the Development Server

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

### 4. Verify Installation

If everything is configured correctly, you should see:
- A black canvas (1080x1920) indicating successful Rendley Engine initialization
- Status showing "Initialized"
- No error messages

## Project Structure

```
├── app/
│   ├── components/
│   │   └── RendleyVideoEditor.tsx    # Main Rendley component
│   ├── page.tsx                      # Home page with video editor
│   └── layout.tsx                    # Root layout
├── .env.local                        # Environment variables (create this)
└── package.json                      # Dependencies including @rendley/sdk
```

## Rendley SDK Usage

The main video editor component is located in `app/components/RendleyVideoEditor.tsx`. It includes:

- **Engine Initialization** - Automatic setup with license validation
- **Canvas Management** - Responsive canvas with proper dimensions
- **Error Handling** - User-friendly error messages and troubleshooting
- **TypeScript Support** - Full type safety for Rendley SDK

### Basic Usage Example

```tsx
import RendleyVideoEditor from './components/RendleyVideoEditor';

export default function MyPage() {
  return (
    <RendleyVideoEditor
      width={1080}
      height={1920}
      backgroundColor="#000000"
    />
  );
}
```

## Troubleshooting

### License Issues
- **Error: "Please configure your Rendley license"**
  - Make sure `.env.local` exists with correct credentials
  - Restart the development server after adding environment variables
  - Verify your license is active at [https://app.rendley.com/](https://app.rendley.com/)

### Canvas Issues
- **Black screen but no errors**: This is normal! The black canvas indicates successful initialization
- **Canvas not showing**: Check browser console for JavaScript errors
- **Responsive issues**: The canvas automatically scales on smaller screens

## Learn More

### Rendley Resources
- [Rendley Documentation](https://docs.rendley.com/) - Complete SDK documentation
- [Quick Start Guide](https://docs.rendley.com/quick-start/installation.html) - Getting started tutorial
- [Create First Video](https://docs.rendley.com/quick-start/create-first-video.html) - Build your first video
- [Examples](https://docs.rendley.com/examples/slideshow-video.html) - Sample projects and tutorials
- [Playground](https://playground.rendley.com) - Interactive examples
- [Discord Community](https://discord.gg/BwdeFFEVXR) - Get help and share projects

### Next.js Resources
- [Next.js Documentation](https://nextjs.org/docs) - Learn about Next.js features and API
- [Learn Next.js](https://nextjs.org/learn) - Interactive Next.js tutorial

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme).

**Important for deployment:**
- Add your Rendley license credentials to Vercel environment variables
- Make sure to prefix them with `NEXT_PUBLIC_` for client-side access

Check out the [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.
