# 🔧 Export Troubleshooting Guide

## ✅ Masalah yang Sudah Diperbaiki

### 1. **Error "Engine is already initialized"**
- **Masalah**: Multiple components mencoba menginisialisasi engine
- **Solusi**: Dibuat `RendleyEngineManager` dengan singleton pattern
- **Status**: ✅ **FIXED**

### 2. **Export Function Error Handling**
- **Masalah**: Export gagal tanpa error handling yang baik
- **Solusi**: Dibuat multiple fallback methods untuk export
- **Status**: ✅ **IMPROVED**

## 🧪 Testing Export Functionality

### Komponen Test yang Tersedia:

#### 1. **TestVideoEditor** (Paling Atas)
- Test export dengan method sederhana
- Test export dengan options advanced
- Logging detail di browser console
- **Gunakan ini untuk debugging export issues**

#### 2. **EasyVideoEditor** (Tengah)
- Export dengan validasi konten
- Multiple fallback methods
- User-friendly error messages

#### 3. **AdvancedVideoEditor** (Bawah)
- Export dengan timeline controls
- Layer management
- Advanced error handling

## 🔍 Cara Debug Export Issues

### Step 1: Buka Browser Console
```javascript
// Buka Developer Tools (F12)
// Lihat tab Console untuk error messages
```

### Step 2: Test dengan TestVideoEditor
1. Scroll ke komponen "🧪 Export Test" di atas
2. Klik "📥 Simple Export" untuk test basic
3. Klik "🚀 Advanced Export" untuk test dengan options
4. Lihat console untuk detailed logs

### Step 3: Cek Error Messages
```javascript
// Common error patterns:
// - "Export returned unexpected format"
// - "All export methods failed"
// - "Export produced empty video file"
```

## 🛠️ Export Methods yang Diimplementasikan

### Method 1: Standard Export
```javascript
const result = await engine.export({
  format: 'mp4',
  quality: 'high',
  fps: 30,
  width: 1080,
  height: 1920,
});
```

### Method 2: Simple Export
```javascript
const result = await engine.export();
```

### Method 3: Render Method (Fallback)
```javascript
const result = await engine.render({
  format: 'mp4',
  quality: 'high',
});
```

### Method 4: Alternative Options
```javascript
const result = await engine.export({
  format: 'mp4',
  quality: 'medium',
  fps: 24,
});
```

## 📋 Export Result Handling

### Supported Return Types:
1. **Direct Blob**: `result instanceof Blob`
2. **Object with Blob**: `result.blob`
3. **URL String**: `result.url` (fetch as blob)
4. **Data URL**: Direct string (fetch as blob)

### Download Implementation:
```javascript
const downloadBlob = (blob, filename) => {
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = filename;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
};
```

## 🚨 Common Issues & Solutions

### Issue 1: "Export returned unexpected format"
**Cause**: Rendley SDK version differences
**Solution**: Multiple fallback methods implemented
**Test**: Use TestVideoEditor to see which method works

### Issue 2: "Export produced empty video file"
**Cause**: No content in timeline or invalid content
**Solution**: 
- Add content before export
- Check layer validation
- Use EasyVideoEditor which validates content

### Issue 3: "All export methods failed"
**Cause**: Engine not properly initialized or license issues
**Solution**:
- Refresh page
- Check console for engine initialization errors
- Verify license is active

### Issue 4: Export hangs/freezes
**Cause**: Large content or browser limitations
**Solution**:
- Try with simpler content first
- Use TestVideoEditor with basic text
- Check browser memory usage

## 🎯 Best Practices untuk Export

### 1. Content Validation
```javascript
// Always check if content exists
if (layers.length <= 2) {
  alert('Please add content before exporting');
  return;
}
```

### 2. Pause Before Export
```javascript
// Stop playback before export
if (isPlaying) {
  await engine.pause();
  setIsPlaying(false);
}
```

### 3. Error Handling
```javascript
try {
  const result = await engine.export(options);
  // Handle result
} catch (err) {
  console.error('Export error:', err);
  // Show user-friendly message
}
```

### 4. Progress Indication
```javascript
setIsExporting(true);
// ... export logic
setIsExporting(false);
```

## 📊 Testing Checklist

- [ ] Engine initializes without "already initialized" error
- [ ] TestVideoEditor shows "✅ Ready" status
- [ ] Simple export works and downloads file
- [ ] Advanced export works with options
- [ ] EasyVideoEditor validates content before export
- [ ] AdvancedVideoEditor handles timeline properly
- [ ] Console shows detailed logs for debugging
- [ ] Downloaded files are valid MP4 format
- [ ] File sizes are reasonable (not 0 bytes)

## 🔗 Useful Console Commands

```javascript
// Check engine status
console.log('Engine initialized:', RendleyEngineManager.getInstance().isEngineInitialized());

// Get engine instance
const engine = RendleyEngineManager.getInstance().getEngine();
console.log('Engine:', engine);

// Test export manually
engine.export().then(result => {
  console.log('Manual export result:', result);
}).catch(err => {
  console.error('Manual export error:', err);
});
```

## 📞 Support

Jika masih ada masalah:
1. Cek browser console untuk error details
2. Test dengan TestVideoEditor component
3. Coba refresh page dan test ulang
4. Pastikan license masih aktif
5. Coba dengan content yang lebih sederhana

**Status**: ✅ Export functionality sudah diperbaiki dengan multiple fallback methods
