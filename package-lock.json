{"name": "my-nextjs-app", "version": "0.1.0", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "my-nextjs-app", "version": "0.1.0", "dependencies": {"@rendley/sdk": "^1.11.5", "next": "15.3.3", "react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "tailwindcss": "^4", "typescript": "^5"}}, "node_modules/@alloc/quick-lru": {"version": "5.2.0", "resolved": "https://registry.npmjs.org/@alloc/quick-lru/-/quick-lru-5.2.0.tgz", "integrity": "sha512-UrcABB+4bUrFABwbluTIBErXwvbsU/V7TZWfmbgJfbkwiBuziS9gxdODUyuiecfdGQ85jglMW6juS3+z5TsKLw==", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/@ampproject/remapping": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/@ampproject/remapping/-/remapping-2.3.0.tgz", "integrity": "sha512-30iZtAPgz+LTIYoeivqYo853f02jBYSd5uGnGpkFV0M3xOt9aN73erkgYAmZU43x4VfqcnLxW9Kpg3R5LC4YYw==", "license": "Apache-2.0", "dependencies": {"@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.24"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@emnapi/runtime": {"version": "1.4.3", "resolved": "https://registry.npmjs.org/@emnapi/runtime/-/runtime-1.4.3.tgz", "integrity": "sha512-pBPWdu6MLKROBX05wSNKcNb++m5Er+KQ9QkB+WVM+pW2Kx9hoSrVTnu3BdkI5eBLZoKu/J6mW/B6i6bJB2ytXQ==", "license": "MIT", "optional": true, "dependencies": {"tslib": "^2.4.0"}}, "node_modules/@img/sharp-darwin-arm64": {"version": "0.34.2", "resolved": "https://registry.npmjs.org/@img/sharp-darwin-arm64/-/sharp-darwin-arm64-0.34.2.tgz", "integrity": "sha512-OfXHZPppddivUJnqyKoi5YVeHRkkNE2zUFT2gbpKxp/JZCFYEYubnMg+gOp6lWfasPrTS+KPosKqdI+ELYVDtg==", "cpu": ["arm64"], "license": "Apache-2.0", "optional": true, "os": ["darwin"], "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "optionalDependencies": {"@img/sharp-libvips-darwin-arm64": "1.1.0"}}, "node_modules/@img/sharp-darwin-x64": {"version": "0.34.2", "resolved": "https://registry.npmjs.org/@img/sharp-darwin-x64/-/sharp-darwin-x64-0.34.2.tgz", "integrity": "sha512-dYvWqmjU9VxqXmjEtjmvHnGqF8GrVjM2Epj9rJ6BUIXvk8slvNDJbhGFvIoXzkDhrJC2jUxNLz/GUjjvSzfw+g==", "cpu": ["x64"], "license": "Apache-2.0", "optional": true, "os": ["darwin"], "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "optionalDependencies": {"@img/sharp-libvips-darwin-x64": "1.1.0"}}, "node_modules/@img/sharp-libvips-darwin-arm64": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/@img/sharp-libvips-darwin-arm64/-/sharp-libvips-darwin-arm64-1.1.0.tgz", "integrity": "sha512-HZ/JUmPwrJSoM4DIQPv/BfNh9yrOA8tlBbqbLz4JZ5uew2+o22Ik+tHQJcih7QJuSa0zo5coHTfD5J8inqj9DA==", "cpu": ["arm64"], "license": "LGPL-3.0-or-later", "optional": true, "os": ["darwin"], "funding": {"url": "https://opencollective.com/libvips"}}, "node_modules/@img/sharp-libvips-darwin-x64": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/@img/sharp-libvips-darwin-x64/-/sharp-libvips-darwin-x64-1.1.0.tgz", "integrity": "sha512-Xzc2ToEmHN+hfvsl9wja0RlnXEgpKNmftriQp6XzY/RaSfwD9th+MSh0WQKzUreLKKINb3afirxW7A0fz2YWuQ==", "cpu": ["x64"], "license": "LGPL-3.0-or-later", "optional": true, "os": ["darwin"], "funding": {"url": "https://opencollective.com/libvips"}}, "node_modules/@img/sharp-libvips-linux-arm": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/@img/sharp-libvips-linux-arm/-/sharp-libvips-linux-arm-1.1.0.tgz", "integrity": "sha512-s8BAd0lwUIvYCJyRdFqvsj+BJIpDBSxs6ivrOPm/R7piTs5UIwY5OjXrP2bqXC9/moGsyRa37eYWYCOGVXxVrA==", "cpu": ["arm"], "license": "LGPL-3.0-or-later", "optional": true, "os": ["linux"], "funding": {"url": "https://opencollective.com/libvips"}}, "node_modules/@img/sharp-libvips-linux-arm64": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/@img/sharp-libvips-linux-arm64/-/sharp-libvips-linux-arm64-1.1.0.tgz", "integrity": "sha512-IVfGJa7gjChDET1dK9SekxFFdflarnUB8PwW8aGwEoF3oAsSDuNUTYS+SKDOyOJxQyDC1aPFMuRYLoDInyV9Ew==", "cpu": ["arm64"], "license": "LGPL-3.0-or-later", "optional": true, "os": ["linux"], "funding": {"url": "https://opencollective.com/libvips"}}, "node_modules/@img/sharp-libvips-linux-ppc64": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/@img/sharp-libvips-linux-ppc64/-/sharp-libvips-linux-ppc64-1.1.0.tgz", "integrity": "sha512-tiXxFZFbhnkWE2LA8oQj7KYR+bWBkiV2nilRldT7bqoEZ4HiDOcePr9wVDAZPi/Id5fT1oY9iGnDq20cwUz8lQ==", "cpu": ["ppc64"], "license": "LGPL-3.0-or-later", "optional": true, "os": ["linux"], "funding": {"url": "https://opencollective.com/libvips"}}, "node_modules/@img/sharp-libvips-linux-s390x": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/@img/sharp-libvips-linux-s390x/-/sharp-libvips-linux-s390x-1.1.0.tgz", "integrity": "sha512-xukSwvhguw7COyzvmjydRb3x/09+21HykyapcZchiCUkTThEQEOMtBj9UhkaBRLuBrgLFzQ2wbxdeCCJW/jgJA==", "cpu": ["s390x"], "license": "LGPL-3.0-or-later", "optional": true, "os": ["linux"], "funding": {"url": "https://opencollective.com/libvips"}}, "node_modules/@img/sharp-libvips-linux-x64": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/@img/sharp-libvips-linux-x64/-/sharp-libvips-linux-x64-1.1.0.tgz", "integrity": "sha512-yRj2+reB8iMg9W5sULM3S74jVS7zqSzHG3Ol/twnAAkAhnGQnpjj6e4ayUz7V+FpKypwgs82xbRdYtchTTUB+Q==", "cpu": ["x64"], "license": "LGPL-3.0-or-later", "optional": true, "os": ["linux"], "funding": {"url": "https://opencollective.com/libvips"}}, "node_modules/@img/sharp-libvips-linuxmusl-arm64": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/@img/sharp-libvips-linuxmusl-arm64/-/sharp-libvips-linuxmusl-arm64-1.1.0.tgz", "integrity": "sha512-jYZdG+whg0MDK+q2COKbYidaqW/WTz0cc1E+tMAusiDygrM4ypmSCjOJPmFTvHHJ8j/6cAGyeDWZOsK06tP33w==", "cpu": ["arm64"], "license": "LGPL-3.0-or-later", "optional": true, "os": ["linux"], "funding": {"url": "https://opencollective.com/libvips"}}, "node_modules/@img/sharp-libvips-linuxmusl-x64": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/@img/sharp-libvips-linuxmusl-x64/-/sharp-libvips-linuxmusl-x64-1.1.0.tgz", "integrity": "sha512-wK7SBdwrAiycjXdkPnGCPLjYb9lD4l6Ze2gSdAGVZrEL05AOUJESWU2lhlC+Ffn5/G+VKuSm6zzbQSzFX/P65A==", "cpu": ["x64"], "license": "LGPL-3.0-or-later", "optional": true, "os": ["linux"], "funding": {"url": "https://opencollective.com/libvips"}}, "node_modules/@img/sharp-linux-arm": {"version": "0.34.2", "resolved": "https://registry.npmjs.org/@img/sharp-linux-arm/-/sharp-linux-arm-0.34.2.tgz", "integrity": "sha512-0DZzkvuEOqQUP9mo2kjjKNok5AmnOr1jB2XYjkaoNRwpAYMDzRmAqUIa1nRi58S2WswqSfPOWLNOr0FDT3H5RQ==", "cpu": ["arm"], "license": "Apache-2.0", "optional": true, "os": ["linux"], "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "optionalDependencies": {"@img/sharp-libvips-linux-arm": "1.1.0"}}, "node_modules/@img/sharp-linux-arm64": {"version": "0.34.2", "resolved": "https://registry.npmjs.org/@img/sharp-linux-arm64/-/sharp-linux-arm64-0.34.2.tgz", "integrity": "sha512-D8n8wgWmPDakc83LORcfJepdOSN6MvWNzzz2ux0MnIbOqdieRZwVYY32zxVx+IFUT8er5KPcyU3XXsn+GzG/0Q==", "cpu": ["arm64"], "license": "Apache-2.0", "optional": true, "os": ["linux"], "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "optionalDependencies": {"@img/sharp-libvips-linux-arm64": "1.1.0"}}, "node_modules/@img/sharp-linux-s390x": {"version": "0.34.2", "resolved": "https://registry.npmjs.org/@img/sharp-linux-s390x/-/sharp-linux-s390x-0.34.2.tgz", "integrity": "sha512-EGZ1xwhBI7dNISwxjChqBGELCWMGDvmxZXKjQRuqMrakhO8QoMgqCrdjnAqJq/CScxfRn+Bb7suXBElKQpPDiw==", "cpu": ["s390x"], "license": "Apache-2.0", "optional": true, "os": ["linux"], "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "optionalDependencies": {"@img/sharp-libvips-linux-s390x": "1.1.0"}}, "node_modules/@img/sharp-linux-x64": {"version": "0.34.2", "resolved": "https://registry.npmjs.org/@img/sharp-linux-x64/-/sharp-linux-x64-0.34.2.tgz", "integrity": "sha512-sD7J+h5nFLMMmOXYH4DD9UtSNBD05tWSSdWAcEyzqW8Cn5UxXvsHAxmxSesYUsTOBmUnjtxghKDl15EvfqLFbQ==", "cpu": ["x64"], "license": "Apache-2.0", "optional": true, "os": ["linux"], "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "optionalDependencies": {"@img/sharp-libvips-linux-x64": "1.1.0"}}, "node_modules/@img/sharp-linuxmusl-arm64": {"version": "0.34.2", "resolved": "https://registry.npmjs.org/@img/sharp-linuxmusl-arm64/-/sharp-linuxmusl-arm64-0.34.2.tgz", "integrity": "sha512-NEE2vQ6wcxYav1/A22OOxoSOGiKnNmDzCYFOZ949xFmrWZOVII1Bp3NqVVpvj+3UeHMFyN5eP/V5hzViQ5CZNA==", "cpu": ["arm64"], "license": "Apache-2.0", "optional": true, "os": ["linux"], "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "optionalDependencies": {"@img/sharp-libvips-linuxmusl-arm64": "1.1.0"}}, "node_modules/@img/sharp-linuxmusl-x64": {"version": "0.34.2", "resolved": "https://registry.npmjs.org/@img/sharp-linuxmusl-x64/-/sharp-linuxmusl-x64-0.34.2.tgz", "integrity": "sha512-DOYMrDm5E6/8bm/yQLCWyuDJwUnlevR8xtF8bs+gjZ7cyUNYXiSf/E8Kp0Ss5xasIaXSHzb888V1BE4i1hFhAA==", "cpu": ["x64"], "license": "Apache-2.0", "optional": true, "os": ["linux"], "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "optionalDependencies": {"@img/sharp-libvips-linuxmusl-x64": "1.1.0"}}, "node_modules/@img/sharp-wasm32": {"version": "0.34.2", "resolved": "https://registry.npmjs.org/@img/sharp-wasm32/-/sharp-wasm32-0.34.2.tgz", "integrity": "sha512-/VI4mdlJ9zkaq53MbIG6rZY+QRN3MLbR6usYlgITEzi4Rpx5S6LFKsycOQjkOGmqTNmkIdLjEvooFKwww6OpdQ==", "cpu": ["wasm32"], "license": "Apache-2.0 AND LGPL-3.0-or-later AND MIT", "optional": true, "dependencies": {"@emnapi/runtime": "^1.4.3"}, "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}}, "node_modules/@img/sharp-win32-arm64": {"version": "0.34.2", "resolved": "https://registry.npmjs.org/@img/sharp-win32-arm64/-/sharp-win32-arm64-0.34.2.tgz", "integrity": "sha512-cfP/r9FdS63VA5k0xiqaNaEoGxBg9k7uE+RQGzuK9fHt7jib4zAVVseR9LsE4gJcNWgT6APKMNnCcnyOtmSEUQ==", "cpu": ["arm64"], "license": "Apache-2.0 AND LGPL-3.0-or-later", "optional": true, "os": ["win32"], "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}}, "node_modules/@img/sharp-win32-ia32": {"version": "0.34.2", "resolved": "https://registry.npmjs.org/@img/sharp-win32-ia32/-/sharp-win32-ia32-0.34.2.tgz", "integrity": "sha512-QLjGGvAbj0X/FXl8n1WbtQ6iVBpWU7JO94u/P2M4a8CFYsvQi4GW2mRy/JqkRx0qpBzaOdKJKw8uc930EX2AHw==", "cpu": ["ia32"], "license": "Apache-2.0 AND LGPL-3.0-or-later", "optional": true, "os": ["win32"], "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}}, "node_modules/@img/sharp-win32-x64": {"version": "0.34.2", "resolved": "https://registry.npmjs.org/@img/sharp-win32-x64/-/sharp-win32-x64-0.34.2.tgz", "integrity": "sha512-aUdT6zEYtDKCaxkofmmJDJYGCf0+pJg3eU9/oBuqvEeoB9dKI6ZLc/1iLJCTuJQDO4ptntAlkUmHgGjyuobZbw==", "cpu": ["x64"], "license": "Apache-2.0 AND LGPL-3.0-or-later", "optional": true, "os": ["win32"], "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}}, "node_modules/@isaacs/fs-minipass": {"version": "4.0.1", "resolved": "https://registry.npmjs.org/@isaacs/fs-minipass/-/fs-minipass-4.0.1.tgz", "integrity": "sha512-wgm9Ehl2jpeqP3zw/7mo3kRHFp5MEDhqAdwy1fTGkHAwnkGOVsgpvQhL8B5n1qlb01jV3n/bI0ZfZp5lWA1k4w==", "dev": true, "license": "ISC", "dependencies": {"minipass": "^7.0.4"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@jridgewell/gen-mapping": {"version": "0.3.8", "resolved": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.8.tgz", "integrity": "sha512-imAbBGkb+ebQyxKgzv5Hu2nmROxoDOXHh80evxdoXNOrvAnVx7zimzc1Oo5h9RlfV4vPXaE2iM5pOFbvOCClWA==", "license": "MIT", "dependencies": {"@jridgewell/set-array": "^1.2.1", "@jridgewell/sourcemap-codec": "^1.4.10", "@jridgewell/trace-mapping": "^0.3.24"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/resolve-uri": {"version": "3.1.2", "resolved": "https://registry.npmjs.org/@jridgewell/resolve-uri/-/resolve-uri-3.1.2.tgz", "integrity": "sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw==", "license": "MIT", "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/set-array": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/@jridgewell/set-array/-/set-array-1.2.1.tgz", "integrity": "sha512-R8gLRTZeyp03ymzP/6Lil/28tGeGEzhx1q2k703KGWRAI1VdvPIXdG70VJc2pAMw3NA6JKL5hhFu1sJX0Mnn/A==", "license": "MIT", "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/sourcemap-codec": {"version": "1.5.0", "resolved": "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.0.tgz", "integrity": "sha512-gv3ZRaISU3fjPAgNsriBRqGWQL6quFx04YMPW/zD8XMLsU32mhCCbfbO6KZFLjvYpCZ8zyDEgqsgf+PwPaM7GQ==", "license": "MIT"}, "node_modules/@jridgewell/trace-mapping": {"version": "0.3.25", "resolved": "https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.25.tgz", "integrity": "sha512-vNk6aEwybGtawWmy/PzwnGDOjCkLWSD2wqvjGGAgOAwCGWySYXfYoxt00IJkTF+8Lb57DwOb3Aa0o9CApepiYQ==", "license": "MIT", "dependencies": {"@jridgewell/resolve-uri": "^3.1.0", "@jridgewell/sourcemap-codec": "^1.4.14"}}, "node_modules/@next/env": {"version": "15.3.3", "resolved": "https://registry.npmjs.org/@next/env/-/env-15.3.3.tgz", "integrity": "sha512-OdiMrzCl2Xi0VTjiQQUK0Xh7bJHnOuET2s+3V+Y40WJBAXrJeGA3f+I8MZJ/YQ3mVGi5XGR1L66oFlgqXhQ4Vw==", "license": "MIT"}, "node_modules/@next/swc-darwin-arm64": {"version": "15.3.3", "resolved": "https://registry.npmjs.org/@next/swc-darwin-arm64/-/swc-darwin-arm64-15.3.3.tgz", "integrity": "sha512-WRJERLuH+O3oYB4yZNVahSVFmtxRNjNF1I1c34tYMoJb0Pve+7/RaLAJJizyYiFhjYNGHRAE1Ri2Fd23zgDqhg==", "cpu": ["arm64"], "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": ">= 10"}}, "node_modules/@next/swc-darwin-x64": {"version": "15.3.3", "resolved": "https://registry.npmjs.org/@next/swc-darwin-x64/-/swc-darwin-x64-15.3.3.tgz", "integrity": "sha512-XHdzH/yBc55lu78k/XwtuFR/ZXUTcflpRXcsu0nKmF45U96jt1tsOZhVrn5YH+paw66zOANpOnFQ9i6/j+UYvw==", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": ">= 10"}}, "node_modules/@next/swc-linux-arm64-gnu": {"version": "15.3.3", "resolved": "https://registry.npmjs.org/@next/swc-linux-arm64-gnu/-/swc-linux-arm64-gnu-15.3.3.tgz", "integrity": "sha512-VZ3sYL2LXB8znNGcjhocikEkag/8xiLgnvQts41tq6i+wql63SMS1Q6N8RVXHw5pEUjiof+II3HkDd7GFcgkzw==", "cpu": ["arm64"], "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">= 10"}}, "node_modules/@next/swc-linux-arm64-musl": {"version": "15.3.3", "resolved": "https://registry.npmjs.org/@next/swc-linux-arm64-musl/-/swc-linux-arm64-musl-15.3.3.tgz", "integrity": "sha512-h6Y1fLU4RWAp1HPNJWDYBQ+e3G7sLckyBXhmH9ajn8l/RSMnhbuPBV/fXmy3muMcVwoJdHL+UtzRzs0nXOf9SA==", "cpu": ["arm64"], "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">= 10"}}, "node_modules/@next/swc-linux-x64-gnu": {"version": "15.3.3", "resolved": "https://registry.npmjs.org/@next/swc-linux-x64-gnu/-/swc-linux-x64-gnu-15.3.3.tgz", "integrity": "sha512-jJ8HRiF3N8Zw6hGlytCj5BiHyG/K+fnTKVDEKvUCyiQ/0r5tgwO7OgaRiOjjRoIx2vwLR+Rz8hQoPrnmFbJdfw==", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">= 10"}}, "node_modules/@next/swc-linux-x64-musl": {"version": "15.3.3", "resolved": "https://registry.npmjs.org/@next/swc-linux-x64-musl/-/swc-linux-x64-musl-15.3.3.tgz", "integrity": "sha512-HrUcTr4N+RgiiGn3jjeT6Oo208UT/7BuTr7K0mdKRBtTbT4v9zJqCDKO97DUqqoBK1qyzP1RwvrWTvU6EPh/Cw==", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">= 10"}}, "node_modules/@next/swc-win32-arm64-msvc": {"version": "15.3.3", "resolved": "https://registry.npmjs.org/@next/swc-win32-arm64-msvc/-/swc-win32-arm64-msvc-15.3.3.tgz", "integrity": "sha512-SxorONgi6K7ZUysMtRF3mIeHC5aA3IQLmKFQzU0OuhuUYwpOBc1ypaLJLP5Bf3M9k53KUUUj4vTPwzGvl/NwlQ==", "cpu": ["arm64"], "license": "MIT", "optional": true, "os": ["win32"], "engines": {"node": ">= 10"}}, "node_modules/@next/swc-win32-x64-msvc": {"version": "15.3.3", "resolved": "https://registry.npmjs.org/@next/swc-win32-x64-msvc/-/swc-win32-x64-msvc-15.3.3.tgz", "integrity": "sha512-4QZG6F8enl9/S2+yIiOiju0iCTFd93d8VC1q9LZS4p/Xuk81W2QDjCFeoogmrWWkAD59z8ZxepBQap2dKS5ruw==", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["win32"], "engines": {"node": ">= 10"}}, "node_modules/@pixi/assets": {"version": "7.4.3", "resolved": "https://registry.npmjs.org/@pixi/assets/-/assets-7.4.3.tgz", "integrity": "sha512-StvjiJBSp/j9hHkGu8AFHNvwYUazXq64WhyhytztyDMRkg/l/cL7EcttY5T0qZNWlIpccdr60LUKrWDOuMpkiw==", "license": "MIT", "peer": true, "dependencies": {"@types/css-font-loading-module": "^0.0.12"}, "peerDependencies": {"@pixi/core": "7.4.3"}}, "node_modules/@pixi/color": {"version": "7.4.3", "resolved": "https://registry.npmjs.org/@pixi/color/-/color-7.4.3.tgz", "integrity": "sha512-a6R+bXKeXMDcRmjYQoBIK+v2EYqxSX49wcjAY579EYM/WrFKS98nSees6lqVUcLKrcQh2DT9srJHX7XMny3voQ==", "license": "MIT", "peer": true, "dependencies": {"@pixi/colord": "^2.9.6"}}, "node_modules/@pixi/colord": {"version": "2.9.6", "resolved": "https://registry.npmjs.org/@pixi/colord/-/colord-2.9.6.tgz", "integrity": "sha512-nezytU2pw587fQstUu1AsJZDVEynjskwOL+kibwcdxsMBFqPsFFNA7xl0ii/gXuDi6M0xj3mfRJj8pBSc2jCfA==", "license": "MIT"}, "node_modules/@pixi/constants": {"version": "7.4.3", "resolved": "https://registry.npmjs.org/@pixi/constants/-/constants-7.4.3.tgz", "integrity": "sha512-QGmwJUNQy/vVEHzL6VGQvnwawLZ1wceZMI8HwJAT4/I2uAzbBeFDdmCS8WsTpSWLZjF/DszDc1D8BFp4pVJ5UQ==", "license": "MIT", "peer": true}, "node_modules/@pixi/core": {"version": "7.4.3", "resolved": "https://registry.npmjs.org/@pixi/core/-/core-7.4.3.tgz", "integrity": "sha512-5YDs11faWgVVTL8VZtLU05/Fl47vaP5Tnsbf+y/WRR0VSW3KhRRGTBU1J3Gdc2xEWbJhUK07KGP7eSZpvtPVgA==", "license": "MIT", "peer": true, "dependencies": {"@pixi/color": "7.4.3", "@pixi/constants": "7.4.3", "@pixi/extensions": "7.4.3", "@pixi/math": "7.4.3", "@pixi/runner": "7.4.3", "@pixi/settings": "7.4.3", "@pixi/ticker": "7.4.3", "@pixi/utils": "7.4.3"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/pixijs"}}, "node_modules/@pixi/display": {"version": "7.4.3", "resolved": "https://registry.npmjs.org/@pixi/display/-/display-7.4.3.tgz", "integrity": "sha512-b5m2dAaoNAVdxz1oDaxl3XZ059NEOcNtGkxTOZ4EYCw/jcp9sZXkgSROHRzsGn4k+NugH7+9MP4Id2Z0kkdUhw==", "license": "MIT", "peer": true, "peerDependencies": {"@pixi/core": "7.4.3"}}, "node_modules/@pixi/extensions": {"version": "7.4.3", "resolved": "https://registry.npmjs.org/@pixi/extensions/-/extensions-7.4.3.tgz", "integrity": "sha512-FhoiYkHQEDYHUE7wXhqfsTRz6KxLXjuMbSiAwnLb9uG1vAgp6q6qd6HEsf4X30YaZbLFY8a4KY6hFZWjF+4Fdw==", "license": "MIT", "peer": true}, "node_modules/@pixi/filter-adjustment": {"version": "5.1.1", "resolved": "https://registry.npmjs.org/@pixi/filter-adjustment/-/filter-adjustment-5.1.1.tgz", "integrity": "sha512-AUHe03rmqXwV1ylAHq62t19AolPWOOYomCcL+Qycb1tf+LbM8FWpGXC6wmU1PkUrhgNc958uM9TrA9nRpplViA==", "license": "MIT", "peerDependencies": {"@pixi/core": "^7.0.0-X"}}, "node_modules/@pixi/filter-advanced-bloom": {"version": "5.1.1", "resolved": "https://registry.npmjs.org/@pixi/filter-advanced-bloom/-/filter-advanced-bloom-5.1.1.tgz", "integrity": "sha512-C5AWmkWKvoYvJ+600qS7rC81E1X1clvrQLw4QE4IiFec5j1b07KhKE78w/BSRYMrBVa0cQ/ju0J1f7XoQYJfdQ==", "license": "MIT", "dependencies": {"@pixi/filter-kawase-blur": "5.1.1"}, "peerDependencies": {"@pixi/core": "^7.0.0-X"}}, "node_modules/@pixi/filter-alpha": {"version": "7.4.3", "resolved": "https://registry.npmjs.org/@pixi/filter-alpha/-/filter-alpha-7.4.3.tgz", "integrity": "sha512-YFdUB1I53USQb+9TEhS849dV2KZhbnNGIoBbOSThUJfXQc4pDguIFWMagVToAQYgmZ4C4AtYfVjaSEELrMcCdA==", "license": "MIT", "peer": true, "peerDependencies": {"@pixi/core": "7.4.3"}}, "node_modules/@pixi/filter-ascii": {"version": "5.1.1", "resolved": "https://registry.npmjs.org/@pixi/filter-ascii/-/filter-ascii-5.1.1.tgz", "integrity": "sha512-uGfpd7aYZuiEzkBH8asL/2j7L/7k/jCZRURjAU9c0unWlkagwIjvUwoPMsdzPNMh2DNQzCG1FPWseSbRFjUNow==", "license": "MIT", "peerDependencies": {"@pixi/core": "^7.0.0-X"}}, "node_modules/@pixi/filter-bevel": {"version": "5.1.1", "resolved": "https://registry.npmjs.org/@pixi/filter-bevel/-/filter-bevel-5.1.1.tgz", "integrity": "sha512-UGik6YEW+fnzVu1DV8ctbxS7eClJQzqaM2sPYI0MopaEE2mW35yjAcg9py9Kwx27BX8FniVRqtJOyKEw1A3mBA==", "license": "MIT", "peerDependencies": {"@pixi/core": "^7.0.0-X"}}, "node_modules/@pixi/filter-bloom": {"version": "5.1.1", "resolved": "https://registry.npmjs.org/@pixi/filter-bloom/-/filter-bloom-5.1.1.tgz", "integrity": "sha512-4/i+tMxAQdgezahxsVCqzkAyBAH4TxtuY/zo1wuCJybEqkKFIzOJ76Y4R/lJevEHS9CGpCTrvjRpup0Hze8k0Q==", "license": "MIT", "peerDependencies": {"@pixi/core": "^7.0.0-X", "@pixi/filter-alpha": "^7.0.0-X", "@pixi/filter-blur": "^7.0.0-X"}}, "node_modules/@pixi/filter-blur": {"version": "7.4.3", "resolved": "https://registry.npmjs.org/@pixi/filter-blur/-/filter-blur-7.4.3.tgz", "integrity": "sha512-ZFzS9L/whdRbs5A/EUgF3yQaBcxNarmbuwaMgrfnpQ84mRczkGByqDLGToadiufyals07ufTrXBGRle9lbtEDA==", "license": "MIT", "peer": true, "peerDependencies": {"@pixi/core": "7.4.3"}}, "node_modules/@pixi/filter-bulge-pinch": {"version": "5.1.1", "resolved": "https://registry.npmjs.org/@pixi/filter-bulge-pinch/-/filter-bulge-pinch-5.1.1.tgz", "integrity": "sha512-80I3g813td7Fnzi7IJSiR3z8gZlKblk6WN+5z6WnscQROcNEpck6lgWS/Lf/IdeHB/FtUKJCbx7RzxkUhiRTvA==", "license": "MIT", "peerDependencies": {"@pixi/core": "^7.0.0-X"}}, "node_modules/@pixi/filter-color-gradient": {"version": "5.3.0", "resolved": "https://registry.npmjs.org/@pixi/filter-color-gradient/-/filter-color-gradient-5.3.0.tgz", "integrity": "sha512-dMgBVkSsHOcmTs7HCmlO2hURsk7sT5sOUfnDw46iGqzGgQ+10gCZNaa/oybrDiyEkj+ijyVQVHwapHS3KUgAUQ==", "license": "MIT", "peerDependencies": {"@pixi/core": "^7.0.0-X"}}, "node_modules/@pixi/filter-color-map": {"version": "5.1.1", "resolved": "https://registry.npmjs.org/@pixi/filter-color-map/-/filter-color-map-5.1.1.tgz", "integrity": "sha512-WvDKvweXkg/t9t40thFlN1d/kUrWXGsxpRpFPNmkrZF6hNxdRjqgfg4wxUOev7uZwHIjcZtTfoLRKhJjF+1uqw==", "license": "MIT", "peerDependencies": {"@pixi/core": "^7.0.0-X"}}, "node_modules/@pixi/filter-color-overlay": {"version": "5.1.1", "resolved": "https://registry.npmjs.org/@pixi/filter-color-overlay/-/filter-color-overlay-5.1.1.tgz", "integrity": "sha512-u8xuWsUePQ1NFBqpxDAFEujW4kImFIIvlp4D2xbRZqJ0RRbeeKEW31Sk4cxl1yFJKWIq0XLyT/TAepT9iIlEXg==", "license": "MIT", "peerDependencies": {"@pixi/core": "^7.0.0-X"}}, "node_modules/@pixi/filter-color-replace": {"version": "5.1.1", "resolved": "https://registry.npmjs.org/@pixi/filter-color-replace/-/filter-color-replace-5.1.1.tgz", "integrity": "sha512-t+FEEnuqvlU1cKMSe8939tIGCNJsqpyc7o5BzIunMxsZsHjMQWzZtWQKls+FSdSlFyk2TWYSXWAxtj2VBzBZBg==", "license": "MIT", "peerDependencies": {"@pixi/core": "^7.0.0-X"}}, "node_modules/@pixi/filter-convolution": {"version": "5.1.1", "resolved": "https://registry.npmjs.org/@pixi/filter-convolution/-/filter-convolution-5.1.1.tgz", "integrity": "sha512-68NNa7lXBFlRgP/ac/L/0bKk/9QvU8urh7CEeOnR9WJxjymglbAa0nM69TBlhg++Fus3t7Mz/jc/GIfPJ/VL6Q==", "license": "MIT", "peerDependencies": {"@pixi/core": "^7.0.0-X"}}, "node_modules/@pixi/filter-cross-hatch": {"version": "5.1.1", "resolved": "https://registry.npmjs.org/@pixi/filter-cross-hatch/-/filter-cross-hatch-5.1.1.tgz", "integrity": "sha512-g1hPHZYmGBpZtGojOtUOBWH6tqhtQGDo5xAp3o3gwmn2QnY087ZiYWFHF5ml+nTL62fEJ78uIpODscz4Y04e8w==", "license": "MIT", "peerDependencies": {"@pixi/core": "^7.0.0-X"}}, "node_modules/@pixi/filter-crt": {"version": "5.1.1", "resolved": "https://registry.npmjs.org/@pixi/filter-crt/-/filter-crt-5.1.1.tgz", "integrity": "sha512-w+rRbR7eTsPf18QPB68Wiyx8laC+v7fYb3hRVhnq/j6yRUJKQgg4HK5KLP9jfUJ9FJvxy4bzLSDQulvxbOMJZg==", "license": "MIT", "peerDependencies": {"@pixi/core": "^7.0.0-X"}}, "node_modules/@pixi/filter-dot": {"version": "5.1.1", "resolved": "https://registry.npmjs.org/@pixi/filter-dot/-/filter-dot-5.1.1.tgz", "integrity": "sha512-w3g6bumHzZgv9ktzegEWQS7OWuHH0QG76sbg/hZBy5K01dyuGAe1uUUnzVN5hZuFTD6q77T2UPlifhNI5j4ixg==", "license": "MIT", "peerDependencies": {"@pixi/core": "^7.0.0-X"}}, "node_modules/@pixi/filter-drop-shadow": {"version": "5.2.0", "resolved": "https://registry.npmjs.org/@pixi/filter-drop-shadow/-/filter-drop-shadow-5.2.0.tgz", "integrity": "sha512-cYS2KDER7cwCu0V4VNSxTHGvzmNcEXdC9j3031YBOkUAE3+p17LMS/TAt6XeMfJV7KaPuusvXy2NFgGkv3RDbw==", "license": "MIT", "dependencies": {"@pixi/filter-kawase-blur": "5.1.1"}, "peerDependencies": {"@pixi/core": "^7.0.0-X"}}, "node_modules/@pixi/filter-emboss": {"version": "5.1.1", "resolved": "https://registry.npmjs.org/@pixi/filter-emboss/-/filter-emboss-5.1.1.tgz", "integrity": "sha512-AoAFVzrMcCXldU+27hvJ95tcKNOVLnanlq1z838l2SzYGgso+ICbLauUz+o2PL/znudUJE6oky+I6WJzeavDsQ==", "license": "MIT", "peerDependencies": {"@pixi/core": "^7.0.0-X"}}, "node_modules/@pixi/filter-glitch": {"version": "5.1.1", "resolved": "https://registry.npmjs.org/@pixi/filter-glitch/-/filter-glitch-5.1.1.tgz", "integrity": "sha512-pl6jOQlzQGg6NwqCwlgioYhlwue2OSRBGByDzh6Y6Y/qxMBuzQi7W56GunhQW79Kpvj9ynDLAGxomvZsrX88qg==", "license": "MIT", "peerDependencies": {"@pixi/core": "^7.0.0-X"}}, "node_modules/@pixi/filter-glow": {"version": "5.2.1", "resolved": "https://registry.npmjs.org/@pixi/filter-glow/-/filter-glow-5.2.1.tgz", "integrity": "sha512-94I4XePDF9yqqA6KQuhPSphEHPJ2lXfqJLn0Bes8VVdwft0Ianj1wALqjoSUeBWqiJbhjBEXGDNkRZhPHvY3Xg==", "license": "MIT", "peerDependencies": {"@pixi/core": "^7.0.0-X"}}, "node_modules/@pixi/filter-godray": {"version": "5.1.1", "resolved": "https://registry.npmjs.org/@pixi/filter-godray/-/filter-godray-5.1.1.tgz", "integrity": "sha512-JCvNiKBF/01VyaBYzeusULg+h6kmBaYg0NruHwe/FaJMWCRIPOUBHMQIUavJR0JGE5s6bEIR8kRtdpf3RHiwqw==", "license": "MIT", "peerDependencies": {"@pixi/core": "^7.0.0-X"}}, "node_modules/@pixi/filter-grayscale": {"version": "5.1.1", "resolved": "https://registry.npmjs.org/@pixi/filter-grayscale/-/filter-grayscale-5.1.1.tgz", "integrity": "sha512-tRyggOhTdAQlQpgH/IzjCbORICua/Gm0JkKGOcdDQOHqt4bTVvAehQ59e2+A6A1yA8pevu2L/C25qQhsPgNW9w==", "license": "MIT", "peerDependencies": {"@pixi/core": "^7.0.0-X"}}, "node_modules/@pixi/filter-hsl-adjustment": {"version": "5.3.0", "resolved": "https://registry.npmjs.org/@pixi/filter-hsl-adjustment/-/filter-hsl-adjustment-5.3.0.tgz", "integrity": "sha512-F7gNLrPADGHkToX5toAp0yYs7FenVtHvbC6oC8fydu7/cuDmH0noobI6ShqTcdWJpJbBk1i9LMS93ifiz/13Aw==", "license": "MIT", "peerDependencies": {"@pixi/core": "^7.0.0-X"}}, "node_modules/@pixi/filter-kawase-blur": {"version": "5.1.1", "resolved": "https://registry.npmjs.org/@pixi/filter-kawase-blur/-/filter-kawase-blur-5.1.1.tgz", "integrity": "sha512-nPnJ1ChBFP+4pgFCwC0RJgHAJCetiHcQU3INH7zCdq88cFABmVmhN+wCKRNg4H7lF1EJjaXgFDkTrTreOD/bnw==", "license": "MIT", "peerDependencies": {"@pixi/core": "^7.0.0-X"}}, "node_modules/@pixi/filter-motion-blur": {"version": "5.1.1", "resolved": "https://registry.npmjs.org/@pixi/filter-motion-blur/-/filter-motion-blur-5.1.1.tgz", "integrity": "sha512-I94s3pW2GutjCyXiKQ/dI4Vl9JKne+Q8QgGRn1mrk0Uwg6DDO/OQI3jqv01S+SCTU3LZqhR/p8AQyxeDmOhr2w==", "license": "MIT", "peerDependencies": {"@pixi/core": "^7.0.0-X"}}, "node_modules/@pixi/filter-multi-color-replace": {"version": "5.1.1", "resolved": "https://registry.npmjs.org/@pixi/filter-multi-color-replace/-/filter-multi-color-replace-5.1.1.tgz", "integrity": "sha512-P1shsJXEOpJGe9FdCUgCMi/nius86lBfb6cDIFM4oXdZfzuBUfWjZfUm7uofOvK7IWSrlXYrYoqp75H0XrLZ8A==", "license": "MIT", "peerDependencies": {"@pixi/core": "^7.0.0-X"}}, "node_modules/@pixi/filter-old-film": {"version": "5.1.1", "resolved": "https://registry.npmjs.org/@pixi/filter-old-film/-/filter-old-film-5.1.1.tgz", "integrity": "sha512-Jgq3eLbcQW48o+YxLe8+T4vsQrvBnKrZAHS3cu3yc2aBLaiVIj8EfYP3vpOPjkQlZ7JVRZNdELpYA6KCR+abXw==", "license": "MIT", "peerDependencies": {"@pixi/core": "^7.0.0-X"}}, "node_modules/@pixi/filter-outline": {"version": "5.2.0", "resolved": "https://registry.npmjs.org/@pixi/filter-outline/-/filter-outline-5.2.0.tgz", "integrity": "sha512-xKfAouhZNKl6A0RvxT5i+2/ean7r16dE/QswwIkbWvr2hhHlp4p9U6XsqdgUERCDxK+IZibMAumbWs4DGxOUeQ==", "license": "MIT", "peerDependencies": {"@pixi/core": "^7.0.0-X"}}, "node_modules/@pixi/filter-pixelate": {"version": "5.1.1", "resolved": "https://registry.npmjs.org/@pixi/filter-pixelate/-/filter-pixelate-5.1.1.tgz", "integrity": "sha512-qTs0Sv10aIbMFW//BPlhcFh1ByyKiVmvXfytYTTXNLrlK5DU3H3x8Pgy5Vy4lacS9VtOO69/CQ1QObBFCHnEBQ==", "license": "MIT", "peerDependencies": {"@pixi/core": "^7.0.0-X"}}, "node_modules/@pixi/filter-radial-blur": {"version": "5.1.1", "resolved": "https://registry.npmjs.org/@pixi/filter-radial-blur/-/filter-radial-blur-5.1.1.tgz", "integrity": "sha512-q6RreUM+RO25HZaxc6ceEOSi6chadv8vrCOvupNLSY+1lvXue0KyFK6vxMcMInNdqRGYWSyJ+ql3RyHMTr93aw==", "license": "MIT", "peerDependencies": {"@pixi/core": "^7.0.0-X"}}, "node_modules/@pixi/filter-reflection": {"version": "5.1.1", "resolved": "https://registry.npmjs.org/@pixi/filter-reflection/-/filter-reflection-5.1.1.tgz", "integrity": "sha512-ksmfrRfBqXZ+rEFuA0l2tf4k+yzTU8VcNMuhW7U+ggkWOP2OEHga+oOlJg6TnHjOEbxudCpag5Us6e9aCeKpEw==", "license": "MIT", "peerDependencies": {"@pixi/core": "^7.0.0-X"}}, "node_modules/@pixi/filter-rgb-split": {"version": "5.1.1", "resolved": "https://registry.npmjs.org/@pixi/filter-rgb-split/-/filter-rgb-split-5.1.1.tgz", "integrity": "sha512-DEeAYoPU2lbUTeNYK8e6q89jqtLeUYSkEdFK/a9IyxYkvJP2CPk+nVXIe48v3wORUf5DdP20k6yQzqoPZyP3ww==", "license": "MIT", "peerDependencies": {"@pixi/core": "^7.0.0-X"}}, "node_modules/@pixi/filter-shockwave": {"version": "5.1.1", "resolved": "https://registry.npmjs.org/@pixi/filter-shockwave/-/filter-shockwave-5.1.1.tgz", "integrity": "sha512-ovzOdAC2LCyWdxJC5PW97wSzHTNfjmKq4c/61cIO4sZp+9DB6n3b/6Rrad2jU346UATtM6K2XkmPY5p7SrRRXA==", "license": "MIT", "peerDependencies": {"@pixi/core": "^7.0.0-X"}}, "node_modules/@pixi/filter-simple-lightmap": {"version": "5.1.1", "resolved": "https://registry.npmjs.org/@pixi/filter-simple-lightmap/-/filter-simple-lightmap-5.1.1.tgz", "integrity": "sha512-S3cHUgbVvUgeef93f3trdL50+162Nyqa7DBYufkGw0dPjPecXyjTH47GJzxDqQPooRwHWWUG9W5EYC+XEwlV9w==", "license": "MIT", "peerDependencies": {"@pixi/core": "^7.0.0-X"}}, "node_modules/@pixi/filter-tilt-shift": {"version": "5.2.0", "resolved": "https://registry.npmjs.org/@pixi/filter-tilt-shift/-/filter-tilt-shift-5.2.0.tgz", "integrity": "sha512-bCQE/BTGsqu8EhRMyiGg+9/FXsPBYxjfODbGTWWQNsXtbFVqZXvg1vEjUZQXvuso1v/Fh/BtZ3u+t2kFfWpBXA==", "license": "MIT", "peerDependencies": {"@pixi/core": "^7.0.0-X"}}, "node_modules/@pixi/filter-twist": {"version": "5.1.1", "resolved": "https://registry.npmjs.org/@pixi/filter-twist/-/filter-twist-5.1.1.tgz", "integrity": "sha512-ZUxLmSHu7ZcP1OYmO9EsKgWDV/Ophf622N7YVei4opBrj/gBMuQZNvFIfnsm4l8yhqAwwzndSTVLNehq1A2ONw==", "license": "MIT", "peerDependencies": {"@pixi/core": "^7.0.0-X"}}, "node_modules/@pixi/filter-zoom-blur": {"version": "5.1.1", "resolved": "https://registry.npmjs.org/@pixi/filter-zoom-blur/-/filter-zoom-blur-5.1.1.tgz", "integrity": "sha512-0n10xOqACC2vm9Lpsq37Y/edDvp/B7xsBdkuWxeCI7Ta7J22fsJ8IHG1iUyxgdZGa+SCPcKiFoTrYEUu5PLCpA==", "license": "MIT", "peerDependencies": {"@pixi/core": "^7.0.0-X"}}, "node_modules/@pixi/gif": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/@pixi/gif/-/gif-2.1.1.tgz", "integrity": "sha512-SGA0HP9/9a2gy3cylBkYOWaA13ffFYj0huGfm9c0QY6AM1XQPpsonzsObIHXgwRjJP+tTjNYU66ML2fqsblM+A==", "license": "MIT", "peerDependencies": {"@pixi/assets": "^7.0.0", "@pixi/core": "^7.0.0", "@pixi/display": "^7.0.0", "@pixi/sprite": "^7.0.0"}}, "node_modules/@pixi/math": {"version": "7.4.3", "resolved": "https://registry.npmjs.org/@pixi/math/-/math-7.4.3.tgz", "integrity": "sha512-/uJOVhR2DOZ+zgdI6Bs/CwcXT4bNRKsS+TqX3ekRIxPCwaLra+Qdm7aDxT5cTToDzdxbKL5+rwiLu3Y1egILDw==", "license": "MIT", "peer": true}, "node_modules/@pixi/runner": {"version": "7.4.3", "resolved": "https://registry.npmjs.org/@pixi/runner/-/runner-7.4.3.tgz", "integrity": "sha512-TJyfp7y23u5vvRAyYhVSa7ytq0PdKSvPLXu4G3meoFh1oxTLHH6g/RIzLuxUAThPG2z7ftthuW3qWq6dRV+dhw==", "license": "MIT", "peer": true}, "node_modules/@pixi/settings": {"version": "7.4.3", "resolved": "https://registry.npmjs.org/@pixi/settings/-/settings-7.4.3.tgz", "integrity": "sha512-SmGK8smc0PxRB9nr0UJioEtE9hl4gvj9OedCvZx3bxBwA3omA5BmP3CyhQfN8XJ29+o2OUL01r3zAPVol4l4lA==", "license": "MIT", "peer": true, "dependencies": {"@pixi/constants": "7.4.3", "@types/css-font-loading-module": "^0.0.12", "ismobilejs": "^1.1.0"}}, "node_modules/@pixi/sprite": {"version": "7.4.3", "resolved": "https://registry.npmjs.org/@pixi/sprite/-/sprite-7.4.3.tgz", "integrity": "sha512-iNBrpOFF9nXDT6m2jcyYy6l/sRzklLDDck1eFHprHZwvNquY2nzRfh+RGBCecxhBcijiLJ3fsZN33fP0LDXkvw==", "license": "MIT", "peer": true, "peerDependencies": {"@pixi/core": "7.4.3", "@pixi/display": "7.4.3"}}, "node_modules/@pixi/ticker": {"version": "7.4.3", "resolved": "https://registry.npmjs.org/@pixi/ticker/-/ticker-7.4.3.tgz", "integrity": "sha512-tHsAD0iOUb6QSGGw+c8cyRBvxsq/NlfzIFBZLEHhWZ+Bx4a0MmXup6I/yJDGmyPCYE+ctCcAfY13wKAzdiVFgQ==", "license": "MIT", "peer": true, "dependencies": {"@pixi/extensions": "7.4.3", "@pixi/settings": "7.4.3", "@pixi/utils": "7.4.3"}}, "node_modules/@pixi/utils": {"version": "7.4.3", "resolved": "https://registry.npmjs.org/@pixi/utils/-/utils-7.4.3.tgz", "integrity": "sha512-NO3Y9HAn2UKS1YdxffqsPp+kDpVm8XWvkZcS/E+rBzY9VTLnNOI7cawSRm+dacdET3a8Jad3aDKEDZ0HmAqAFA==", "license": "MIT", "peer": true, "dependencies": {"@pixi/color": "7.4.3", "@pixi/constants": "7.4.3", "@pixi/settings": "7.4.3", "@types/earcut": "^2.1.0", "earcut": "^2.2.4", "eventemitter3": "^4.0.0", "url": "^0.11.0"}}, "node_modules/@pixi/utils/node_modules/eventemitter3": {"version": "4.0.7", "resolved": "https://registry.npmjs.org/eventemitter3/-/eventemitter3-4.0.7.tgz", "integrity": "sha512-8guHBZCwKnFhYdHr2ysuRWErTwhoN2X8XELRlrRwpmfeY2jjuUN4taQMsULKUVo1K4DvZl+0pgfyoysHxvmvEw==", "license": "MIT", "peer": true}, "node_modules/@rendley/sdk": {"version": "1.11.5", "resolved": "https://registry.npmjs.org/@rendley/sdk/-/sdk-1.11.5.tgz", "integrity": "sha512-aVW3LGTL7X7kQdN4dhloBasuNfo3Eu99U2dTG9tDXkmUR5SV4s1iJ+iMAvv/Q59UBya01P77Wkhy6yqzqRJCHA==", "license": "LICENSE", "dependencies": {"@pixi/gif": "2.1.1", "@tweenjs/tween.js": "23.1.2", "bodymovin": "4.13.0", "crypto-js": "4.2.0", "eventemitter3": "5.0.1", "gradient-parser": "1.0.2", "lottie-web": "5.12.2", "pixi-filters": "^5.3.0", "pixi.js": "7.4.0", "unplugin-preprocessor-directives": "^1.0.3", "uuid": "9.0.1", "zod": "3.22.4"}}, "node_modules/@swc/counter": {"version": "0.1.3", "resolved": "https://registry.npmjs.org/@swc/counter/-/counter-0.1.3.tgz", "integrity": "sha512-e2BR4lsJkkRlKZ/qCHPw9ZaSxc0MVUd7gtbtaB7aMvHeJVYe8sOB8DBZkP2DtISHGSku9sCK6T6cnY0CtXrOCQ==", "license": "Apache-2.0"}, "node_modules/@swc/helpers": {"version": "0.5.15", "resolved": "https://registry.npmjs.org/@swc/helpers/-/helpers-0.5.15.tgz", "integrity": "sha512-JQ5TuMi45Owi4/BIMAJBoSQoOJu12oOk/gADqlcUL9JEdHB8vyjUSsxqeNXnmXHjYKMi2WcYtezGEEhqUI/E2g==", "license": "Apache-2.0", "dependencies": {"tslib": "^2.8.0"}}, "node_modules/@tailwindcss/node": {"version": "4.1.8", "resolved": "https://registry.npmjs.org/@tailwindcss/node/-/node-4.1.8.tgz", "integrity": "sha512-OWwBsbC9BFAJelmnNcrKuf+bka2ZxCE2A4Ft53Tkg4uoiE67r/PMEYwCsourC26E+kmxfwE0hVzMdxqeW+xu7Q==", "dev": true, "license": "MIT", "dependencies": {"@ampproject/remapping": "^2.3.0", "enhanced-resolve": "^5.18.1", "jiti": "^2.4.2", "lightningcss": "1.30.1", "magic-string": "^0.30.17", "source-map-js": "^1.2.1", "tailwindcss": "4.1.8"}}, "node_modules/@tailwindcss/oxide": {"version": "4.1.8", "resolved": "https://registry.npmjs.org/@tailwindcss/oxide/-/oxide-4.1.8.tgz", "integrity": "sha512-d7qvv9PsM5N3VNKhwVUhpK6r4h9wtLkJ6lz9ZY9aeZgrUWk1Z8VPyqyDT9MZlem7GTGseRQHkeB1j3tC7W1P+A==", "dev": true, "hasInstallScript": true, "license": "MIT", "dependencies": {"detect-libc": "^2.0.4", "tar": "^7.4.3"}, "engines": {"node": ">= 10"}, "optionalDependencies": {"@tailwindcss/oxide-android-arm64": "4.1.8", "@tailwindcss/oxide-darwin-arm64": "4.1.8", "@tailwindcss/oxide-darwin-x64": "4.1.8", "@tailwindcss/oxide-freebsd-x64": "4.1.8", "@tailwindcss/oxide-linux-arm-gnueabihf": "4.1.8", "@tailwindcss/oxide-linux-arm64-gnu": "4.1.8", "@tailwindcss/oxide-linux-arm64-musl": "4.1.8", "@tailwindcss/oxide-linux-x64-gnu": "4.1.8", "@tailwindcss/oxide-linux-x64-musl": "4.1.8", "@tailwindcss/oxide-wasm32-wasi": "4.1.8", "@tailwindcss/oxide-win32-arm64-msvc": "4.1.8", "@tailwindcss/oxide-win32-x64-msvc": "4.1.8"}}, "node_modules/@tailwindcss/oxide-android-arm64": {"version": "4.1.8", "resolved": "https://registry.npmjs.org/@tailwindcss/oxide-android-arm64/-/oxide-android-arm64-4.1.8.tgz", "integrity": "sha512-Fbz7qni62uKYceWYvUjRqhGfZKwhZDQhlrJKGtnZfuNtHFqa8wmr+Wn74CTWERiW2hn3mN5gTpOoxWKk0jRxjg==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["android"], "engines": {"node": ">= 10"}}, "node_modules/@tailwindcss/oxide-darwin-arm64": {"version": "4.1.8", "resolved": "https://registry.npmjs.org/@tailwindcss/oxide-darwin-arm64/-/oxide-darwin-arm64-4.1.8.tgz", "integrity": "sha512-RdRvedGsT0vwVVDztvyXhKpsU2ark/BjgG0huo4+2BluxdXo8NDgzl77qh0T1nUxmM11eXwR8jA39ibvSTbi7A==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": ">= 10"}}, "node_modules/@tailwindcss/oxide-darwin-x64": {"version": "4.1.8", "resolved": "https://registry.npmjs.org/@tailwindcss/oxide-darwin-x64/-/oxide-darwin-x64-4.1.8.tgz", "integrity": "sha512-t6PgxjEMLp5Ovf7uMb2OFmb3kqzVTPPakWpBIFzppk4JE4ix0yEtbtSjPbU8+PZETpaYMtXvss2Sdkx8Vs4XRw==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": ">= 10"}}, "node_modules/@tailwindcss/oxide-freebsd-x64": {"version": "4.1.8", "resolved": "https://registry.npmjs.org/@tailwindcss/oxide-freebsd-x64/-/oxide-freebsd-x64-4.1.8.tgz", "integrity": "sha512-g8C8eGEyhHTqwPStSwZNSrOlyx0bhK/V/+zX0Y+n7DoRUzyS8eMbVshVOLJTDDC+Qn9IJnilYbIKzpB9n4aBsg==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["freebsd"], "engines": {"node": ">= 10"}}, "node_modules/@tailwindcss/oxide-linux-arm-gnueabihf": {"version": "4.1.8", "resolved": "https://registry.npmjs.org/@tailwindcss/oxide-linux-arm-gnueabihf/-/oxide-linux-arm-gnueabihf-4.1.8.tgz", "integrity": "sha512-Jmzr3FA4S2tHhaC6yCjac3rGf7hG9R6Gf2z9i9JFcuyy0u79HfQsh/thifbYTF2ic82KJovKKkIB6Z9TdNhCXQ==", "cpu": ["arm"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">= 10"}}, "node_modules/@tailwindcss/oxide-linux-arm64-gnu": {"version": "4.1.8", "resolved": "https://registry.npmjs.org/@tailwindcss/oxide-linux-arm64-gnu/-/oxide-linux-arm64-gnu-4.1.8.tgz", "integrity": "sha512-qq7jXtO1+UEtCmCeBBIRDrPFIVI4ilEQ97qgBGdwXAARrUqSn/L9fUrkb1XP/mvVtoVeR2bt/0L77xx53bPZ/Q==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">= 10"}}, "node_modules/@tailwindcss/oxide-linux-arm64-musl": {"version": "4.1.8", "resolved": "https://registry.npmjs.org/@tailwindcss/oxide-linux-arm64-musl/-/oxide-linux-arm64-musl-4.1.8.tgz", "integrity": "sha512-O6b8QesPbJCRshsNApsOIpzKt3ztG35gfX9tEf4arD7mwNinsoCKxkj8TgEE0YRjmjtO3r9FlJnT/ENd9EVefQ==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">= 10"}}, "node_modules/@tailwindcss/oxide-linux-x64-gnu": {"version": "4.1.8", "resolved": "https://registry.npmjs.org/@tailwindcss/oxide-linux-x64-gnu/-/oxide-linux-x64-gnu-4.1.8.tgz", "integrity": "sha512-32iEXX/pXwikshNOGnERAFwFSfiltmijMIAbUhnNyjFr3tmWmMJWQKU2vNcFX0DACSXJ3ZWcSkzNbaKTdngH6g==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">= 10"}}, "node_modules/@tailwindcss/oxide-linux-x64-musl": {"version": "4.1.8", "resolved": "https://registry.npmjs.org/@tailwindcss/oxide-linux-x64-musl/-/oxide-linux-x64-musl-4.1.8.tgz", "integrity": "sha512-s+VSSD+TfZeMEsCaFaHTaY5YNj3Dri8rST09gMvYQKwPphacRG7wbuQ5ZJMIJXN/puxPcg/nU+ucvWguPpvBDg==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">= 10"}}, "node_modules/@tailwindcss/oxide-wasm32-wasi": {"version": "4.1.8", "resolved": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-4.1.8.tgz", "integrity": "sha512-CXBPVFkpDjM67sS1psWohZ6g/2/cd+cq56vPxK4JeawelxwK4YECgl9Y9TjkE2qfF+9/s1tHHJqrC4SS6cVvSg==", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "cpu": ["wasm32"], "dev": true, "license": "MIT", "optional": true, "dependencies": {"@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.10", "@tybys/wasm-util": "^0.9.0", "tslib": "^2.8.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@tailwindcss/oxide-win32-arm64-msvc": {"version": "4.1.8", "resolved": "https://registry.npmjs.org/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-4.1.8.tgz", "integrity": "sha512-7GmYk1n28teDHUjPlIx4Z6Z4hHEgvP5ZW2QS9ygnDAdI/myh3HTHjDqtSqgu1BpRoI4OiLx+fThAyA1JePoENA==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["win32"], "engines": {"node": ">= 10"}}, "node_modules/@tailwindcss/oxide-win32-x64-msvc": {"version": "4.1.8", "resolved": "https://registry.npmjs.org/@tailwindcss/oxide-win32-x64-msvc/-/oxide-win32-x64-msvc-4.1.8.tgz", "integrity": "sha512-fou+U20j+Jl0EHwK92spoWISON2OBnCazIc038Xj2TdweYV33ZRkS9nwqiUi2d/Wba5xg5UoHfvynnb/UB49cQ==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["win32"], "engines": {"node": ">= 10"}}, "node_modules/@tailwindcss/postcss": {"version": "4.1.8", "resolved": "https://registry.npmjs.org/@tailwindcss/postcss/-/postcss-4.1.8.tgz", "integrity": "sha512-vB/vlf7rIky+w94aWMw34bWW1ka6g6C3xIOdICKX2GC0VcLtL6fhlLiafF0DVIwa9V6EHz8kbWMkS2s2QvvNlw==", "dev": true, "license": "MIT", "dependencies": {"@alloc/quick-lru": "^5.2.0", "@tailwindcss/node": "4.1.8", "@tailwindcss/oxide": "4.1.8", "postcss": "^8.4.41", "tailwindcss": "4.1.8"}}, "node_modules/@tweenjs/tween.js": {"version": "23.1.2", "resolved": "https://registry.npmjs.org/@tweenjs/tween.js/-/tween.js-23.1.2.tgz", "integrity": "sha512-kMCNaZCJugWI86xiEHaY338CU5JpD0B97p1j1IKNn/Zto8PgACjQx0UxbHjmOcLl/dDOBnItwD07KmCs75pxtQ==", "license": "MIT"}, "node_modules/@types/css-font-loading-module": {"version": "0.0.12", "resolved": "https://registry.npmjs.org/@types/css-font-loading-module/-/css-font-loading-module-0.0.12.tgz", "integrity": "sha512-x2tZZYkSxXqWvTDgveSynfjq/T2HyiZHXb00j/+gy19yp70PHCizM48XFdjBCWH7eHBD0R5i/pw9yMBP/BH5uA==", "license": "MIT"}, "node_modules/@types/earcut": {"version": "2.1.4", "resolved": "https://registry.npmjs.org/@types/earcut/-/earcut-2.1.4.tgz", "integrity": "sha512-qp3m9PPz4gULB9MhjGID7wpo3gJ4bTGXm7ltNDsmOvsPduTeHp8wSW9YckBj3mljeOh4F0m2z/0JKAALRKbmLQ==", "license": "MIT"}, "node_modules/@types/node": {"version": "20.19.0", "resolved": "https://registry.npmjs.org/@types/node/-/node-20.19.0.tgz", "integrity": "sha512-hfrc+1tud1xcdVTABC2JiomZJEklMcXYNTVtZLAeqTVWD+qL5jkHKT+1lOtqDdGxt+mB53DTtiz673vfjU8D1Q==", "dev": true, "license": "MIT", "dependencies": {"undici-types": "~6.21.0"}}, "node_modules/@types/react": {"version": "19.1.6", "resolved": "https://registry.npmjs.org/@types/react/-/react-19.1.6.tgz", "integrity": "sha512-JeG0rEWak0N6Itr6QUx+X60uQmN+5t3j9r/OVDtWzFXKaj6kD1BwJzOksD0FF6iWxZlbE1kB0q9vtnU2ekqa1Q==", "dev": true, "license": "MIT", "dependencies": {"csstype": "^3.0.2"}}, "node_modules/@types/react-dom": {"version": "19.1.6", "resolved": "https://registry.npmjs.org/@types/react-dom/-/react-dom-19.1.6.tgz", "integrity": "sha512-4hOiT/dwO8Ko0gV1m/TJZYk3y0KBnY9vzDh7W+DH17b2HFSOGgdj33dhihPeuy3l0q23+4e+hoXHV6hCC4dCXw==", "dev": true, "license": "MIT", "peerDependencies": {"@types/react": "^19.0.0"}}, "node_modules/acorn": {"version": "8.14.1", "resolved": "https://registry.npmjs.org/acorn/-/acorn-8.14.1.tgz", "integrity": "sha512-OvQ/2pUDKmgfCg++xsTX1wGxfTaszcHVcTctW4UJB4hibJx2HXxxO5UmVgyjMa+ZDsiaf5wWLXYpRWMmBI0QHg==", "license": "MIT", "bin": {"acorn": "bin/acorn"}, "engines": {"node": ">=0.4.0"}}, "node_modules/bodymovin": {"version": "4.13.0", "resolved": "https://registry.npmjs.org/bodymovin/-/bodymovin-4.13.0.tgz", "integrity": "sha512-SdBh+kue99zDxUGe0tpX7HJ8LMpRac46jcHcWAdHJDW9Aa5Dhcc62uGn03XQS8soF5/NWx7KBzPadgm+FbtDGA==", "license": "MIT"}, "node_modules/busboy": {"version": "1.6.0", "resolved": "https://registry.npmjs.org/busboy/-/busboy-1.6.0.tgz", "integrity": "sha512-8SFQbg/0hQ9xy3UNTB0YEnsNBbWfhf7RtnzpL7TkBiTBRfrQ9Fxcnz7VJsleJpyp6rVLvXiuORqjlHi5q+PYuA==", "dependencies": {"streamsearch": "^1.1.0"}, "engines": {"node": ">=10.16.0"}}, "node_modules/call-bind-apply-helpers": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/call-bind-apply-helpers/-/call-bind-apply-helpers-1.0.2.tgz", "integrity": "sha512-Sp1ablJ0ivDkSzjcaJdxEunN5/XvksFJ2sMBFfq6x0ryhQV/2b/KwFe21cMpmHtPOSij8K99/wSfoEuTObmuMQ==", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/call-bound": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/call-bound/-/call-bound-1.0.4.tgz", "integrity": "sha512-+ys997U96po4Kx/ABpBCqhA9EuxJaQWDQg7295H4hBphv3IZg0boBKuwYpt4YXp6MZ5AmZQnU/tyMTlRpaSejg==", "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.2", "get-intrinsic": "^1.3.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/caniuse-lite": {"version": "1.0.30001721", "resolved": "https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001721.tgz", "integrity": "sha512-cOuvmUVtKrtEaoKiO0rSc29jcjwMwX5tOHDy4MgVFEWiUXj4uBMJkwI8MDySkgXidpMiHUcviogAvFi4pA2hDQ==", "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/caniuse-lite"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "CC-BY-4.0"}, "node_modules/chownr": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/chownr/-/chownr-3.0.0.tgz", "integrity": "sha512-+IxzY9BZOQd/XuYPRmrvEVjF/nqj5kgT4kEq7VofrDoM1MxoRjEWkrCC3EtLi59TVawxTAn+orJwFQcrqEN1+g==", "dev": true, "license": "BlueOak-1.0.0", "engines": {"node": ">=18"}}, "node_modules/client-only": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/client-only/-/client-only-0.0.1.tgz", "integrity": "sha512-IV3Ou0jSMzZrd3pZ48nLkT9DA7Ag1pnPzaiQhpW7c3RbcqqzvzzVu+L8gfqMp/8IM2MQtSiqaCxrrcfu8I8rMA==", "license": "MIT"}, "node_modules/color": {"version": "4.2.3", "resolved": "https://registry.npmjs.org/color/-/color-4.2.3.tgz", "integrity": "sha512-1rXeuUUiGGrykh+CeBdu5Ie7OJwinCgQY0bc7GCRxy5xVHy+moaqkpL/jqQq0MtQOeYcrqEz4abc5f0KtU7W4A==", "license": "MIT", "optional": true, "dependencies": {"color-convert": "^2.0.1", "color-string": "^1.9.0"}, "engines": {"node": ">=12.5.0"}}, "node_modules/color-convert": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/color-convert/-/color-convert-2.0.1.tgz", "integrity": "sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==", "license": "MIT", "optional": true, "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/color-name": {"version": "1.1.4", "resolved": "https://registry.npmjs.org/color-name/-/color-name-1.1.4.tgz", "integrity": "sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==", "license": "MIT", "optional": true}, "node_modules/color-string": {"version": "1.9.1", "resolved": "https://registry.npmjs.org/color-string/-/color-string-1.9.1.tgz", "integrity": "sha512-shrVawQFojnZv6xM40anx4CkoDP+fZsw/ZerEMsW/pyzsRbElpsL/DBVW7q3ExxwusdNXI3lXpuhEZkzs8p5Eg==", "license": "MIT", "optional": true, "dependencies": {"color-name": "^1.0.0", "simple-swizzle": "^0.2.2"}}, "node_modules/crypto-js": {"version": "4.2.0", "resolved": "https://registry.npmjs.org/crypto-js/-/crypto-js-4.2.0.tgz", "integrity": "sha512-KALDyEYgpY+Rlob/iriUtjV6d5Eq+Y191A5g4UqLAi8CyGP9N1+FdVbkc1SxKc2r4YAYqG8JzO2KGL+AizD70Q==", "license": "MIT"}, "node_modules/csstype": {"version": "3.1.3", "resolved": "https://registry.npmjs.org/csstype/-/csstype-3.1.3.tgz", "integrity": "sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw==", "dev": true, "license": "MIT"}, "node_modules/detect-libc": {"version": "2.0.4", "resolved": "https://registry.npmjs.org/detect-libc/-/detect-libc-2.0.4.tgz", "integrity": "sha512-3UDv+G9CsCKO1WKMGw9fwq/SWJYbI0c5Y7LU1AXYoDdbhE2AHQ6N6Nb34sG8Fj7T5APy8qXDCKuuIHd1BR0tVA==", "devOptional": true, "license": "Apache-2.0", "engines": {"node": ">=8"}}, "node_modules/dunder-proto": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/dunder-proto/-/dunder-proto-1.0.1.tgz", "integrity": "sha512-KIN/nDJBQRcXw0MLVhZE9iQHmG68qAVIBg9CqmUYjmQIhgij9U5MFvrqkUL5FbtyyzZuOeOt0zdeRe4UY7ct+A==", "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.1", "es-errors": "^1.3.0", "gopd": "^1.2.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/earcut": {"version": "2.2.4", "resolved": "https://registry.npmjs.org/earcut/-/earcut-2.2.4.tgz", "integrity": "sha512-/pjZsA1b4RPHbeWZQn66SWS8nZZWLQQ23oE3Eam7aroEFGEvwKAsJfZ9ytiEMycfzXWpca4FA9QIOehf7PocBQ==", "license": "ISC"}, "node_modules/enhanced-resolve": {"version": "5.18.1", "resolved": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-5.18.1.tgz", "integrity": "sha512-ZSW3ma5GkcQBIpwZTSRAI8N71Uuwgs93IezB7mf7R60tC8ZbJideoDNKjHn2O9KIlx6rkGTTEk1xUCK2E1Y2Yg==", "dev": true, "license": "MIT", "dependencies": {"graceful-fs": "^4.2.4", "tapable": "^2.2.0"}, "engines": {"node": ">=10.13.0"}}, "node_modules/es-define-property": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/es-define-property/-/es-define-property-1.0.1.tgz", "integrity": "sha512-e3nRfgfUZ4rNGL232gUgX06QNyyez04KdjFrF+LTRoOXmrOgFKDg4BCdsjW8EnT69eqdYGmRpJwiPVYNrCaW3g==", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/es-errors": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/es-errors/-/es-errors-1.3.0.tgz", "integrity": "sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/es-object-atoms": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/es-object-atoms/-/es-object-atoms-1.1.1.tgz", "integrity": "sha512-FGgH2h8zKNim9ljj7dankFPcICIK9Cp5bm+c2gQSYePhpaG5+esrLODihIorn+Pe6FGJzWhXQotPv73jTaldXA==", "license": "MIT", "dependencies": {"es-errors": "^1.3.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/eventemitter3": {"version": "5.0.1", "resolved": "https://registry.npmjs.org/eventemitter3/-/eventemitter3-5.0.1.tgz", "integrity": "sha512-GWkBvjiSZK87ELrYOSESUYeVIc9mvLLf/nXalMOS5dYrgZq9o5OVkbZAVM06CVxYsCwH9BDZFPlQTlPA1j4ahA==", "license": "MIT"}, "node_modules/function-bind": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/function-bind/-/function-bind-1.1.2.tgz", "integrity": "sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==", "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/get-intrinsic": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.3.0.tgz", "integrity": "sha512-9fSjSaos/fRIVIp+xSJlE6lfwhES7LNtKaCBIamHsjr2na1BiABJPo0mOjjz8GJDURarmCPGqaiVg5mfjb98CQ==", "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.2", "es-define-property": "^1.0.1", "es-errors": "^1.3.0", "es-object-atoms": "^1.1.1", "function-bind": "^1.1.2", "get-proto": "^1.0.1", "gopd": "^1.2.0", "has-symbols": "^1.1.0", "hasown": "^2.0.2", "math-intrinsics": "^1.1.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/get-proto": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/get-proto/-/get-proto-1.0.1.tgz", "integrity": "sha512-sTSfBjoXBp89JvIKIefqw7U2CCebsc74kiY6awiGogKtoSGbgjYE/G/+l9sF3MWFPNc9IcoOC4ODfKHfxFmp0g==", "license": "MIT", "dependencies": {"dunder-proto": "^1.0.1", "es-object-atoms": "^1.0.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/gopd": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/gopd/-/gopd-1.2.0.tgz", "integrity": "sha512-ZUKRh6/kUFoAiTAtTYPZJ3hw9wNxx+BIBOijnlG9PnrJsCcSjs1wyyD6vJpaYtgnzDrKYRSqf3OO6Rfa93xsRg==", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/graceful-fs": {"version": "4.2.11", "resolved": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.11.tgz", "integrity": "sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==", "dev": true, "license": "ISC"}, "node_modules/gradient-parser": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/gradient-parser/-/gradient-parser-1.0.2.tgz", "integrity": "sha512-gR6nY33xC9yJoH4wGLQtZQMXDi6RI3H37ERu7kQCVUzlXjNedpZM7xcA489Opwbq0BSGohtWGsWsntupmxelMg==", "engines": {"node": ">=0.10.0"}}, "node_modules/has-symbols": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/has-symbols/-/has-symbols-1.1.0.tgz", "integrity": "sha512-1cDNdwJ2Jaohmb3sg4OmKaMBwuC48sYni5HUw2DvsC8LjGTLK9h+eb1X6RyuOHe4hT0ULCW68iomhjUoKUqlPQ==", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/hasown": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/hasown/-/hasown-2.0.2.tgz", "integrity": "sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==", "license": "MIT", "dependencies": {"function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/is-arrayish": {"version": "0.3.2", "resolved": "https://registry.npmjs.org/is-arrayish/-/is-arrayish-0.3.2.tgz", "integrity": "sha512-eVRqCvVlZbuw3GrM63ovNSNAeA1K16kaR/LRY/92w0zxQ5/1YzwblUX652i4Xs9RwAGjW9d9y6X88t8OaAJfWQ==", "license": "MIT", "optional": true}, "node_modules/ismobilejs": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/ismobilejs/-/ismobilejs-1.1.1.tgz", "integrity": "sha512-VaFW53yt8QO61k2WJui0dHf4SlL8lxBofUuUmwBo0ljPk0Drz2TiuDW4jo3wDcv41qy/SxrJ+VAzJ/qYqsmzRw==", "license": "MIT"}, "node_modules/jiti": {"version": "2.4.2", "resolved": "https://registry.npmjs.org/jiti/-/jiti-2.4.2.tgz", "integrity": "sha512-rg9zJN+G4n2nfJl5MW3BMygZX56zKPNVEYYqq7adpmMh4Jn2QNEwhvQlFy6jPVdcod7txZtKHWnyZiA3a0zP7A==", "dev": true, "license": "MIT", "bin": {"jiti": "lib/jiti-cli.mjs"}}, "node_modules/lightningcss": {"version": "1.30.1", "resolved": "https://registry.npmjs.org/lightningcss/-/lightningcss-1.30.1.tgz", "integrity": "sha512-xi6IyHML+c9+Q3W0S4fCQJOym42pyurFiJUHEcEyHS0CeKzia4yZDEsLlqOFykxOdHpNy0NmvVO31vcSqAxJCg==", "dev": true, "license": "MPL-2.0", "dependencies": {"detect-libc": "^2.0.3"}, "engines": {"node": ">= 12.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "optionalDependencies": {"lightningcss-darwin-arm64": "1.30.1", "lightningcss-darwin-x64": "1.30.1", "lightningcss-freebsd-x64": "1.30.1", "lightningcss-linux-arm-gnueabihf": "1.30.1", "lightningcss-linux-arm64-gnu": "1.30.1", "lightningcss-linux-arm64-musl": "1.30.1", "lightningcss-linux-x64-gnu": "1.30.1", "lightningcss-linux-x64-musl": "1.30.1", "lightningcss-win32-arm64-msvc": "1.30.1", "lightningcss-win32-x64-msvc": "1.30.1"}}, "node_modules/lightningcss-darwin-arm64": {"version": "1.30.1", "resolved": "https://registry.npmjs.org/lightningcss-darwin-arm64/-/lightningcss-darwin-arm64-1.30.1.tgz", "integrity": "sha512-c8JK7hyE65X1MHMN+Viq9n11RRC7hgin3HhYKhrMyaXflk5GVplZ60IxyoVtzILeKr+xAJwg6zK6sjTBJ0FKYQ==", "cpu": ["arm64"], "dev": true, "license": "MPL-2.0", "optional": true, "os": ["darwin"], "engines": {"node": ">= 12.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/lightningcss-darwin-x64": {"version": "1.30.1", "resolved": "https://registry.npmjs.org/lightningcss-darwin-x64/-/lightningcss-darwin-x64-1.30.1.tgz", "integrity": "sha512-k1EvjakfumAQoTfcXUcHQZhSpLlkAuEkdMBsI/ivWw9hL+7FtilQc0Cy3hrx0AAQrVtQAbMI7YjCgYgvn37PzA==", "cpu": ["x64"], "dev": true, "license": "MPL-2.0", "optional": true, "os": ["darwin"], "engines": {"node": ">= 12.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/lightningcss-freebsd-x64": {"version": "1.30.1", "resolved": "https://registry.npmjs.org/lightningcss-freebsd-x64/-/lightningcss-freebsd-x64-1.30.1.tgz", "integrity": "sha512-kmW6UGCGg2PcyUE59K5r0kWfKPAVy4SltVeut+umLCFoJ53RdCUWxcRDzO1eTaxf/7Q2H7LTquFHPL5R+Gjyig==", "cpu": ["x64"], "dev": true, "license": "MPL-2.0", "optional": true, "os": ["freebsd"], "engines": {"node": ">= 12.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/lightningcss-linux-arm-gnueabihf": {"version": "1.30.1", "resolved": "https://registry.npmjs.org/lightningcss-linux-arm-gnueabihf/-/lightningcss-linux-arm-gnueabihf-1.30.1.tgz", "integrity": "sha512-MjxUShl1v8pit+6D/zSPq9S9dQ2NPFSQwGvxBCYaBYLPlCWuPh9/t1MRS8iUaR8i+a6w7aps+B4N0S1TYP/R+Q==", "cpu": ["arm"], "dev": true, "license": "MPL-2.0", "optional": true, "os": ["linux"], "engines": {"node": ">= 12.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/lightningcss-linux-arm64-gnu": {"version": "1.30.1", "resolved": "https://registry.npmjs.org/lightningcss-linux-arm64-gnu/-/lightningcss-linux-arm64-gnu-1.30.1.tgz", "integrity": "sha512-gB72maP8rmrKsnKYy8XUuXi/4OctJiuQjcuqWNlJQ6jZiWqtPvqFziskH3hnajfvKB27ynbVCucKSm2rkQp4Bw==", "cpu": ["arm64"], "dev": true, "license": "MPL-2.0", "optional": true, "os": ["linux"], "engines": {"node": ">= 12.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/lightningcss-linux-arm64-musl": {"version": "1.30.1", "resolved": "https://registry.npmjs.org/lightningcss-linux-arm64-musl/-/lightningcss-linux-arm64-musl-1.30.1.tgz", "integrity": "sha512-jmUQVx4331m6LIX+0wUhBbmMX7TCfjF5FoOH6SD1CttzuYlGNVpA7QnrmLxrsub43ClTINfGSYyHe2HWeLl5CQ==", "cpu": ["arm64"], "dev": true, "license": "MPL-2.0", "optional": true, "os": ["linux"], "engines": {"node": ">= 12.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/lightningcss-linux-x64-gnu": {"version": "1.30.1", "resolved": "https://registry.npmjs.org/lightningcss-linux-x64-gnu/-/lightningcss-linux-x64-gnu-1.30.1.tgz", "integrity": "sha512-piWx3z4wN8J8z3+O5kO74+yr6ze/dKmPnI7vLqfSqI8bccaTGY5xiSGVIJBDd5K5BHlvVLpUB3S2YCfelyJ1bw==", "cpu": ["x64"], "dev": true, "license": "MPL-2.0", "optional": true, "os": ["linux"], "engines": {"node": ">= 12.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/lightningcss-linux-x64-musl": {"version": "1.30.1", "resolved": "https://registry.npmjs.org/lightningcss-linux-x64-musl/-/lightningcss-linux-x64-musl-1.30.1.tgz", "integrity": "sha512-rRomAK7eIkL+tHY0YPxbc5Dra2gXlI63HL+v1Pdi1a3sC+tJTcFrHX+E86sulgAXeI7rSzDYhPSeHHjqFhqfeQ==", "cpu": ["x64"], "dev": true, "license": "MPL-2.0", "optional": true, "os": ["linux"], "engines": {"node": ">= 12.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/lightningcss-win32-arm64-msvc": {"version": "1.30.1", "resolved": "https://registry.npmjs.org/lightningcss-win32-arm64-msvc/-/lightningcss-win32-arm64-msvc-1.30.1.tgz", "integrity": "sha512-mSL4rqPi4iXq5YVqzSsJgMVFENoa4nGTT/GjO2c0Yl9OuQfPsIfncvLrEW6RbbB24WtZ3xP/2CCmI3tNkNV4oA==", "cpu": ["arm64"], "dev": true, "license": "MPL-2.0", "optional": true, "os": ["win32"], "engines": {"node": ">= 12.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/lightningcss-win32-x64-msvc": {"version": "1.30.1", "resolved": "https://registry.npmjs.org/lightningcss-win32-x64-msvc/-/lightningcss-win32-x64-msvc-1.30.1.tgz", "integrity": "sha512-PVqXh48wh4T53F/1CCu8PIPCxLzWyCnn/9T5W1Jpmdy5h9Cwd+0YQS6/LwhHXSafuc61/xg9Lv5OrCby6a++jg==", "cpu": ["x64"], "dev": true, "license": "MPL-2.0", "optional": true, "os": ["win32"], "engines": {"node": ">= 12.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/lottie-web": {"version": "5.12.2", "resolved": "https://registry.npmjs.org/lottie-web/-/lottie-web-5.12.2.tgz", "integrity": "sha512-uvhvYPC8kGPjXT3MyKMrL3JitEAmDMp30lVkuq/590Mw9ok6pWcFCwXJveo0t5uqYw1UREQHofD+jVpdjBv8wg==", "license": "MIT"}, "node_modules/magic-string": {"version": "0.30.17", "resolved": "https://registry.npmjs.org/magic-string/-/magic-string-0.30.17.tgz", "integrity": "sha512-sNPKHvyjVf7gyjwS4xGTaW/mCnF8wnjtifKBEhxfZ7E/S8tQ0rssrwGNn6q8JH/ohItJfSQp9mBtQYuTlH5QnA==", "license": "MIT", "dependencies": {"@jridgewell/sourcemap-codec": "^1.5.0"}}, "node_modules/math-intrinsics": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/math-intrinsics/-/math-intrinsics-1.1.0.tgz", "integrity": "sha512-/IXtbwEk5HTPyEwyKX6hGkYXxM9nbj64B+ilVJnC/R6B0pH5G4V3b0pVbL7DBj4tkhBAppbQUlf6F6Xl9LHu1g==", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/minipass": {"version": "7.1.2", "resolved": "https://registry.npmjs.org/minipass/-/minipass-7.1.2.tgz", "integrity": "sha512-qOOzS1cBTWYF4BH8fVePDBOO9iptMnGUEZwNc/cMWnTV2nVLZ7VoNWEPHkYczZA0pdoA7dl6e7FL659nX9S2aw==", "dev": true, "license": "ISC", "engines": {"node": ">=16 || 14 >=14.17"}}, "node_modules/minizlib": {"version": "3.0.2", "resolved": "https://registry.npmjs.org/minizlib/-/minizlib-3.0.2.tgz", "integrity": "sha512-oG62iEk+CYt5Xj2YqI5Xi9xWUeZhDI8jjQmC5oThVH5JGCTgIjr7ciJDzC7MBzYd//WvR1OTmP5Q38Q8ShQtVA==", "dev": true, "license": "MIT", "dependencies": {"minipass": "^7.1.2"}, "engines": {"node": ">= 18"}}, "node_modules/mkdirp": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/mkdirp/-/mkdirp-3.0.1.tgz", "integrity": "sha512-+NsyUUAZDmo6YVHzL/stxSu3t9YS1iljliy3BSDrXJ/dkn1KYdmtZODGGjLcc9XLgVVpH4KshHB8XmZgMhaBXg==", "dev": true, "license": "MIT", "bin": {"mkdirp": "dist/cjs/src/bin.js"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/nanoid": {"version": "3.3.11", "resolved": "https://registry.npmjs.org/nanoid/-/nanoid-3.3.11.tgz", "integrity": "sha512-N8SpfPUnUp1bK+PMYW8qSWdl9U+wwNWI4QKxOYDy9JAro3WMX7p2OeVRF9v+347pnakNevPmiHhNmZ2HbFA76w==", "funding": [{"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "bin": {"nanoid": "bin/nanoid.cjs"}, "engines": {"node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"}}, "node_modules/next": {"version": "15.3.3", "resolved": "https://registry.npmjs.org/next/-/next-15.3.3.tgz", "integrity": "sha512-JqNj29hHNmCLtNvd090SyRbXJiivQ+58XjCcrC50Crb5g5u2zi7Y2YivbsEfzk6AtVI80akdOQbaMZwWB1Hthw==", "license": "MIT", "dependencies": {"@next/env": "15.3.3", "@swc/counter": "0.1.3", "@swc/helpers": "0.5.15", "busboy": "1.6.0", "caniuse-lite": "^1.0.30001579", "postcss": "8.4.31", "styled-jsx": "5.1.6"}, "bin": {"next": "dist/bin/next"}, "engines": {"node": "^18.18.0 || ^19.8.0 || >= 20.0.0"}, "optionalDependencies": {"@next/swc-darwin-arm64": "15.3.3", "@next/swc-darwin-x64": "15.3.3", "@next/swc-linux-arm64-gnu": "15.3.3", "@next/swc-linux-arm64-musl": "15.3.3", "@next/swc-linux-x64-gnu": "15.3.3", "@next/swc-linux-x64-musl": "15.3.3", "@next/swc-win32-arm64-msvc": "15.3.3", "@next/swc-win32-x64-msvc": "15.3.3", "sharp": "^0.34.1"}, "peerDependencies": {"@opentelemetry/api": "^1.1.0", "@playwright/test": "^1.41.2", "babel-plugin-react-compiler": "*", "react": "^18.2.0 || 19.0.0-rc-de68d2f4-20241204 || ^19.0.0", "react-dom": "^18.2.0 || 19.0.0-rc-de68d2f4-20241204 || ^19.0.0", "sass": "^1.3.0"}, "peerDependenciesMeta": {"@opentelemetry/api": {"optional": true}, "@playwright/test": {"optional": true}, "babel-plugin-react-compiler": {"optional": true}, "sass": {"optional": true}}}, "node_modules/next/node_modules/postcss": {"version": "8.4.31", "resolved": "https://registry.npmjs.org/postcss/-/postcss-8.4.31.tgz", "integrity": "sha512-PS08Iboia9mts/2ygV3eLpY5ghnUcfLV/EXTOW1E2qYxJKGGBUtNjN76FYHnMs36RmARn41bC0AZmn+rR0OVpQ==", "funding": [{"type": "opencollective", "url": "https://opencollective.com/postcss/"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/postcss"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"nanoid": "^3.3.6", "picocolors": "^1.0.0", "source-map-js": "^1.0.2"}, "engines": {"node": "^10 || ^12 || >=14"}}, "node_modules/object-inspect": {"version": "1.13.4", "resolved": "https://registry.npmjs.org/object-inspect/-/object-inspect-1.13.4.tgz", "integrity": "sha512-W67iLl4J2EXEGTbfeHCffrjDfitvLANg0UlX3wFUUSTx92KXRFegMHUVgSqE+wvhAbi4WqjGg9czysTV2Epbew==", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/picocolors": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/picocolors/-/picocolors-1.1.1.tgz", "integrity": "sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA==", "license": "ISC"}, "node_modules/pixi-filters": {"version": "5.3.0", "resolved": "https://registry.npmjs.org/pixi-filters/-/pixi-filters-5.3.0.tgz", "integrity": "sha512-Dyop7gqgHl5Jc/Bm0QH0CQIDogdICp3hf2CihvgI4AvQebIotPY3yX2MZ/YUcE/7b2aIQQQ2CCt/uc2bBnLRAg==", "license": "MIT", "dependencies": {"@pixi/filter-adjustment": "5.1.1", "@pixi/filter-advanced-bloom": "5.1.1", "@pixi/filter-ascii": "5.1.1", "@pixi/filter-bevel": "5.1.1", "@pixi/filter-bloom": "5.1.1", "@pixi/filter-bulge-pinch": "5.1.1", "@pixi/filter-color-gradient": "5.3.0", "@pixi/filter-color-map": "5.1.1", "@pixi/filter-color-overlay": "5.1.1", "@pixi/filter-color-replace": "5.1.1", "@pixi/filter-convolution": "5.1.1", "@pixi/filter-cross-hatch": "5.1.1", "@pixi/filter-crt": "5.1.1", "@pixi/filter-dot": "5.1.1", "@pixi/filter-drop-shadow": "5.2.0", "@pixi/filter-emboss": "5.1.1", "@pixi/filter-glitch": "5.1.1", "@pixi/filter-glow": "5.2.1", "@pixi/filter-godray": "5.1.1", "@pixi/filter-grayscale": "5.1.1", "@pixi/filter-hsl-adjustment": "5.3.0", "@pixi/filter-kawase-blur": "5.1.1", "@pixi/filter-motion-blur": "5.1.1", "@pixi/filter-multi-color-replace": "5.1.1", "@pixi/filter-old-film": "5.1.1", "@pixi/filter-outline": "5.2.0", "@pixi/filter-pixelate": "5.1.1", "@pixi/filter-radial-blur": "5.1.1", "@pixi/filter-reflection": "5.1.1", "@pixi/filter-rgb-split": "5.1.1", "@pixi/filter-shockwave": "5.1.1", "@pixi/filter-simple-lightmap": "5.1.1", "@pixi/filter-tilt-shift": "5.2.0", "@pixi/filter-twist": "5.1.1", "@pixi/filter-zoom-blur": "5.1.1"}}, "node_modules/pixi.js": {"version": "7.4.0", "resolved": "https://registry.npmjs.org/pixi.js/-/pixi.js-7.4.0.tgz", "integrity": "sha512-c2q3NG06RcSzgcyNieuC/ogzdaBKRoZvBlAiPdL8ubhJyEVCoSA+zitjsCe/m3t5cVrrjPnwo81ps+fg908hBw==", "license": "MIT", "dependencies": {"@pixi/accessibility": "7.4.0", "@pixi/app": "7.4.0", "@pixi/assets": "7.4.0", "@pixi/compressed-textures": "7.4.0", "@pixi/core": "7.4.0", "@pixi/display": "7.4.0", "@pixi/events": "7.4.0", "@pixi/extensions": "7.4.0", "@pixi/extract": "7.4.0", "@pixi/filter-alpha": "7.4.0", "@pixi/filter-blur": "7.4.0", "@pixi/filter-color-matrix": "7.4.0", "@pixi/filter-displacement": "7.4.0", "@pixi/filter-fxaa": "7.4.0", "@pixi/filter-noise": "7.4.0", "@pixi/graphics": "7.4.0", "@pixi/mesh": "7.4.0", "@pixi/mesh-extras": "7.4.0", "@pixi/mixin-cache-as-bitmap": "7.4.0", "@pixi/mixin-get-child-by-name": "7.4.0", "@pixi/mixin-get-global-position": "7.4.0", "@pixi/particle-container": "7.4.0", "@pixi/prepare": "7.4.0", "@pixi/sprite": "7.4.0", "@pixi/sprite-animated": "7.4.0", "@pixi/sprite-tiling": "7.4.0", "@pixi/spritesheet": "7.4.0", "@pixi/text": "7.4.0", "@pixi/text-bitmap": "7.4.0", "@pixi/text-html": "7.4.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/pixijs"}}, "node_modules/pixi.js/node_modules/@pixi/accessibility": {"version": "7.4.0", "resolved": "https://registry.npmjs.org/@pixi/accessibility/-/accessibility-7.4.0.tgz", "integrity": "sha512-muosfpn333YNz2s7mtoVlKvcXswFOJ4r+5rePn3r/95KQIpuB+xX6pETuzGq0p8uOpKxtkNokGj5s2dyM0blHA==", "license": "MIT", "peerDependencies": {"@pixi/core": "7.4.0", "@pixi/display": "7.4.0", "@pixi/events": "7.4.0"}}, "node_modules/pixi.js/node_modules/@pixi/app": {"version": "7.4.0", "resolved": "https://registry.npmjs.org/@pixi/app/-/app-7.4.0.tgz", "integrity": "sha512-9pDB974rfuObG5YHvR7kdWhDiIV26b0GeC4vHRQB3bkmltguMi8SCQ9WQKH3WwRLaflzf9EMZpgX10cU1gLgKg==", "license": "MIT", "peerDependencies": {"@pixi/core": "7.4.0", "@pixi/display": "7.4.0"}}, "node_modules/pixi.js/node_modules/@pixi/assets": {"version": "7.4.0", "resolved": "https://registry.npmjs.org/@pixi/assets/-/assets-7.4.0.tgz", "integrity": "sha512-Z7J2ZYSZ41Pr3CK0IXgtVV1HiLm1sG0AOZHAPMwB82wNdIDvmWowo/LkXvQmSHFLxFlEz1hWOdOFs1daWAeIfg==", "license": "MIT", "dependencies": {"@types/css-font-loading-module": "^0.0.12"}, "peerDependencies": {"@pixi/core": "7.4.0"}}, "node_modules/pixi.js/node_modules/@pixi/color": {"version": "7.4.0", "resolved": "https://registry.npmjs.org/@pixi/color/-/color-7.4.0.tgz", "integrity": "sha512-Qgn3OSW9SFCQ8wrm524anENwIAeRTORC014LkTqaBQrpuOUHrx11SCy4kNFaQyZWO1DCTe4m8g/foCK7zJM7cg==", "license": "MIT", "dependencies": {"@pixi/colord": "^2.9.6"}}, "node_modules/pixi.js/node_modules/@pixi/compressed-textures": {"version": "7.4.0", "resolved": "https://registry.npmjs.org/@pixi/compressed-textures/-/compressed-textures-7.4.0.tgz", "integrity": "sha512-M9bpOFeUPuss57mbRtJOD8cGh+X8xsfx8YMBqWzQTAfbA8hsTQ+O4arbMTyIxqZnaTvpmhlhTKwaVaI2V15NAg==", "license": "MIT", "peerDependencies": {"@pixi/assets": "7.4.0", "@pixi/core": "7.4.0"}}, "node_modules/pixi.js/node_modules/@pixi/constants": {"version": "7.4.0", "resolved": "https://registry.npmjs.org/@pixi/constants/-/constants-7.4.0.tgz", "integrity": "sha512-jQMPMRqkOTjI4D0cHWqvu+pofw6bIa8861x2vp2kNsmM2zcBO/b01AlmILi5pEDk0nTumgzgmVHZ7dtT9KxfQw==", "license": "MIT"}, "node_modules/pixi.js/node_modules/@pixi/core": {"version": "7.4.0", "resolved": "https://registry.npmjs.org/@pixi/core/-/core-7.4.0.tgz", "integrity": "sha512-X6UiDzmdd2oRK3zQggDrWNIlw5rjZakByRIwI6MRgj17FGkpNkCY78dO1snZ6qnpUoo5M03aSUCFCfq6LKA5Bg==", "license": "MIT", "dependencies": {"@pixi/color": "7.4.0", "@pixi/constants": "7.4.0", "@pixi/extensions": "7.4.0", "@pixi/math": "7.4.0", "@pixi/runner": "7.4.0", "@pixi/settings": "7.4.0", "@pixi/ticker": "7.4.0", "@pixi/utils": "7.4.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/pixijs"}}, "node_modules/pixi.js/node_modules/@pixi/display": {"version": "7.4.0", "resolved": "https://registry.npmjs.org/@pixi/display/-/display-7.4.0.tgz", "integrity": "sha512-l+K6H9CqB2tQltpaxal3dIPPAOWhBWszrJm5EbK5sVVQFcaWXgeS/Hmniz0DhT7OpPmstcx4nii9hZgRkmMmEg==", "license": "MIT", "peerDependencies": {"@pixi/core": "7.4.0"}}, "node_modules/pixi.js/node_modules/@pixi/events": {"version": "7.4.0", "resolved": "https://registry.npmjs.org/@pixi/events/-/events-7.4.0.tgz", "integrity": "sha512-9hshDahiFDbl3ZJt9cqutST+2aIZ8/bT29VVFuN2f0ZHatbEHVl46jqu0IL8d+TAlNUr+SI/JEaPA6/MR9sH6w==", "license": "MIT", "peerDependencies": {"@pixi/core": "7.4.0", "@pixi/display": "7.4.0"}}, "node_modules/pixi.js/node_modules/@pixi/extensions": {"version": "7.4.0", "resolved": "https://registry.npmjs.org/@pixi/extensions/-/extensions-7.4.0.tgz", "integrity": "sha512-bX0aw6z2D9bJ5NOsrbuWXnBR7sy2z+dyq2EQ2/t0dF6Si764r8FiA0QUGFn9NJO1FTnB9LLjz7q4c0XaWF3mcg==", "license": "MIT"}, "node_modules/pixi.js/node_modules/@pixi/extract": {"version": "7.4.0", "resolved": "https://registry.npmjs.org/@pixi/extract/-/extract-7.4.0.tgz", "integrity": "sha512-PLOdi8LxnRBRTKLx5plA9hWsIObiQ44tKMcyaLIESXNoUGE3135Aih10Hg1whrQcG4n9EqRjNak7LtwKRylRbg==", "license": "MIT", "peerDependencies": {"@pixi/core": "7.4.0"}}, "node_modules/pixi.js/node_modules/@pixi/filter-alpha": {"version": "7.4.0", "resolved": "https://registry.npmjs.org/@pixi/filter-alpha/-/filter-alpha-7.4.0.tgz", "integrity": "sha512-1KjdTcU4drduzF1HDu1clxZgM7b6lfE1CKESlY5CizJSMMGcycOUQRq/TWK54xrsJTyPWwNu5ojma6dcIqLOrw==", "license": "MIT", "peerDependencies": {"@pixi/core": "7.4.0"}}, "node_modules/pixi.js/node_modules/@pixi/filter-blur": {"version": "7.4.0", "resolved": "https://registry.npmjs.org/@pixi/filter-blur/-/filter-blur-7.4.0.tgz", "integrity": "sha512-XUrhswyuc4+flpDL0fQcRuei8ctgYCdTxCuetSqpS+qdf4gOJyq5UyCwDycJiudZD6+R23svUX5OQOPwkWTsNA==", "license": "MIT", "peerDependencies": {"@pixi/core": "7.4.0"}}, "node_modules/pixi.js/node_modules/@pixi/filter-color-matrix": {"version": "7.4.0", "resolved": "https://registry.npmjs.org/@pixi/filter-color-matrix/-/filter-color-matrix-7.4.0.tgz", "integrity": "sha512-Ap5Fh6iJo5Mk6xMTia5KAWj9G0b4F3LiqrrWkM0y9gGzD5ei85Hd+XHHJtzWi+d4P/EWv7KlND6SnVcTZFgV4A==", "license": "MIT", "peerDependencies": {"@pixi/core": "7.4.0"}}, "node_modules/pixi.js/node_modules/@pixi/filter-displacement": {"version": "7.4.0", "resolved": "https://registry.npmjs.org/@pixi/filter-displacement/-/filter-displacement-7.4.0.tgz", "integrity": "sha512-fcFLxFge2V6o7LqIsz/goDTMbwLdHjGggbu9/t4+byNP5f+S2TTR3oT4nulTYhNQph5vyllhSPJgHoqXXRhTwg==", "license": "MIT", "peerDependencies": {"@pixi/core": "7.4.0"}}, "node_modules/pixi.js/node_modules/@pixi/filter-fxaa": {"version": "7.4.0", "resolved": "https://registry.npmjs.org/@pixi/filter-fxaa/-/filter-fxaa-7.4.0.tgz", "integrity": "sha512-W4l01ca9hJpjAfswRkw6UaCNh76E9ymigSVIBzhUUFwjfvVvIh7+O9SnEzkTVHsY15ANsznD0XZjgt3pW/wFbg==", "license": "MIT", "peerDependencies": {"@pixi/core": "7.4.0"}}, "node_modules/pixi.js/node_modules/@pixi/filter-noise": {"version": "7.4.0", "resolved": "https://registry.npmjs.org/@pixi/filter-noise/-/filter-noise-7.4.0.tgz", "integrity": "sha512-q2+CWODAJO79j0StJ+xakX4D8r8w/RLURRiyG+focTIj1ws/7sdDmDsV+jmeKm6pEktwgA3JYWIKZUnezlGf8g==", "license": "MIT", "peerDependencies": {"@pixi/core": "7.4.0"}}, "node_modules/pixi.js/node_modules/@pixi/graphics": {"version": "7.4.0", "resolved": "https://registry.npmjs.org/@pixi/graphics/-/graphics-7.4.0.tgz", "integrity": "sha512-9GcXbP/iTFEA5xwXx6sSwGyIYPd6XVhFJR7ALqqnlYC+FvvvHPoh7cN3HPa1Aw9dWpNRKUKuNcoOYPmd0O0aJA==", "license": "MIT", "peerDependencies": {"@pixi/core": "7.4.0", "@pixi/display": "7.4.0", "@pixi/sprite": "7.4.0"}}, "node_modules/pixi.js/node_modules/@pixi/math": {"version": "7.4.0", "resolved": "https://registry.npmjs.org/@pixi/math/-/math-7.4.0.tgz", "integrity": "sha512-9WCWKX5z/VOYGpsnXXQ73vg/IT+bUXCLO6miXuS5YPXNfw9RpvdV4ZgFmuQwPNM7wfFk5T7Uvfr8ZJRBCfKhZw==", "license": "MIT"}, "node_modules/pixi.js/node_modules/@pixi/mesh": {"version": "7.4.0", "resolved": "https://registry.npmjs.org/@pixi/mesh/-/mesh-7.4.0.tgz", "integrity": "sha512-Ql5B3q8UD898LTKTPAkveOU72tN9xD8CsLPuvmPSrjpE5FlyRhrS90JzD26/sz6H3B7Kfu2gRjilmujCzNvuWA==", "license": "MIT", "peerDependencies": {"@pixi/core": "7.4.0", "@pixi/display": "7.4.0"}}, "node_modules/pixi.js/node_modules/@pixi/mesh-extras": {"version": "7.4.0", "resolved": "https://registry.npmjs.org/@pixi/mesh-extras/-/mesh-extras-7.4.0.tgz", "integrity": "sha512-YMI72eDruRd3iUIxfFNW+siuwvvrBv4/A9GDeBySKdfqbMOnzi0GLjxvF88bcP7eujdJQDwzTnAV4hW0UNIkjw==", "license": "MIT", "peerDependencies": {"@pixi/core": "7.4.0", "@pixi/mesh": "7.4.0"}}, "node_modules/pixi.js/node_modules/@pixi/mixin-cache-as-bitmap": {"version": "7.4.0", "resolved": "https://registry.npmjs.org/@pixi/mixin-cache-as-bitmap/-/mixin-cache-as-bitmap-7.4.0.tgz", "integrity": "sha512-wFkwU19dCyY5m0JxiKf6UJwvR8XaGDWA/0VXZelBF+WwIj54uKjN4lNSnSApHHByFfq9BRka7B5C1fU9eZNOzg==", "license": "MIT", "peerDependencies": {"@pixi/core": "7.4.0", "@pixi/display": "7.4.0", "@pixi/sprite": "7.4.0"}}, "node_modules/pixi.js/node_modules/@pixi/mixin-get-child-by-name": {"version": "7.4.0", "resolved": "https://registry.npmjs.org/@pixi/mixin-get-child-by-name/-/mixin-get-child-by-name-7.4.0.tgz", "integrity": "sha512-GAWXSNnYtZyppxGVpt0lN2Iq6Z1MYuGeE/X5rYd5yO+Ra9VbUaslTRxf2y8H1TTWOPCIs8mcSTNdJTgElSfqbQ==", "license": "MIT", "peerDependencies": {"@pixi/display": "7.4.0"}}, "node_modules/pixi.js/node_modules/@pixi/mixin-get-global-position": {"version": "7.4.0", "resolved": "https://registry.npmjs.org/@pixi/mixin-get-global-position/-/mixin-get-global-position-7.4.0.tgz", "integrity": "sha512-u2EKXi7sv1zG2exk/bpjozBTOElBAsHnA0sHe0kz6sELpNBjv4g2n0Hwfl+qd69S+60zfN44ER+ihbFUWgD5VA==", "license": "MIT", "peerDependencies": {"@pixi/core": "7.4.0", "@pixi/display": "7.4.0"}}, "node_modules/pixi.js/node_modules/@pixi/particle-container": {"version": "7.4.0", "resolved": "https://registry.npmjs.org/@pixi/particle-container/-/particle-container-7.4.0.tgz", "integrity": "sha512-y3cB2EvgzfOm/pw4qBFsKOVoRzhzLy/FFj92DbD3bL5a6Z+YtKblkeWw3P5exzZJBTRn9sEk1vhzBb1HM/WEJw==", "license": "MIT", "peerDependencies": {"@pixi/core": "7.4.0", "@pixi/display": "7.4.0", "@pixi/sprite": "7.4.0"}}, "node_modules/pixi.js/node_modules/@pixi/prepare": {"version": "7.4.0", "resolved": "https://registry.npmjs.org/@pixi/prepare/-/prepare-7.4.0.tgz", "integrity": "sha512-qMRf0SPVYW6k0ZG19SdddwH/FErywEzkJtS7pCVrFy31RP4dF+ZunEffKNPm3Kf5b94JXd6+lIAxDy4tDVqXNQ==", "license": "MIT", "peerDependencies": {"@pixi/core": "7.4.0", "@pixi/display": "7.4.0", "@pixi/graphics": "7.4.0", "@pixi/text": "7.4.0"}}, "node_modules/pixi.js/node_modules/@pixi/runner": {"version": "7.4.0", "resolved": "https://registry.npmjs.org/@pixi/runner/-/runner-7.4.0.tgz", "integrity": "sha512-TIfocv2TD4xHOiGSpeu2y3GMN09cKEpxiS/rswdCU/aacfgSyvjEmskL/dbq/PSA4FCmjVHLyjgNPvd79WPZhQ==", "license": "MIT"}, "node_modules/pixi.js/node_modules/@pixi/settings": {"version": "7.4.0", "resolved": "https://registry.npmjs.org/@pixi/settings/-/settings-7.4.0.tgz", "integrity": "sha512-ODWmSVfLnn384xFsXp+NNV6mQ+AwoeI4FtN+tMcJ+w/qQTi+eq6VLIpgqNx2Z/TJESI4HY4jxL6qz4SJEs7SMA==", "license": "MIT", "dependencies": {"@pixi/constants": "7.4.0", "@types/css-font-loading-module": "^0.0.12", "ismobilejs": "^1.1.0"}}, "node_modules/pixi.js/node_modules/@pixi/sprite": {"version": "7.4.0", "resolved": "https://registry.npmjs.org/@pixi/sprite/-/sprite-7.4.0.tgz", "integrity": "sha512-+yQdq3aKS59s9uxiW066geWLCKYTRjtbdgE2qtyUP4pK/bYanWVWash7K8P3qVX8NQsQKjGvNPoa2fkP6MBE1Q==", "license": "MIT", "peerDependencies": {"@pixi/core": "7.4.0", "@pixi/display": "7.4.0"}}, "node_modules/pixi.js/node_modules/@pixi/sprite-animated": {"version": "7.4.0", "resolved": "https://registry.npmjs.org/@pixi/sprite-animated/-/sprite-animated-7.4.0.tgz", "integrity": "sha512-SVIO78hHqVvBg5kh13TES0oqmjBhjeQmCgXVzT1nC62Vxh/6AAd9JOKid706lXoqRgw7H7OhdunEWL6J2zN4KA==", "license": "MIT", "peerDependencies": {"@pixi/core": "7.4.0", "@pixi/sprite": "7.4.0"}}, "node_modules/pixi.js/node_modules/@pixi/sprite-tiling": {"version": "7.4.0", "resolved": "https://registry.npmjs.org/@pixi/sprite-tiling/-/sprite-tiling-7.4.0.tgz", "integrity": "sha512-q0wjrdhvqnfSRNYIJ0KHUIT0nARvlmBoKBtjEZLAnk1jQCFzrJIg4qfmsBNDSOzMVaAxAot0EbOLjld6EZmf8w==", "license": "MIT", "peerDependencies": {"@pixi/core": "7.4.0", "@pixi/display": "7.4.0", "@pixi/sprite": "7.4.0"}}, "node_modules/pixi.js/node_modules/@pixi/spritesheet": {"version": "7.4.0", "resolved": "https://registry.npmjs.org/@pixi/spritesheet/-/spritesheet-7.4.0.tgz", "integrity": "sha512-wztt4ne71AWDY4WMyuoMUrZlYVeKkubRTqT9HcPYxDEClxZAz1ggsr03PB4RGHbNQkVC1ImrAi9fa0D0PkyPYg==", "license": "MIT", "peerDependencies": {"@pixi/assets": "7.4.0", "@pixi/core": "7.4.0"}}, "node_modules/pixi.js/node_modules/@pixi/text": {"version": "7.4.0", "resolved": "https://registry.npmjs.org/@pixi/text/-/text-7.4.0.tgz", "integrity": "sha512-yVVeWYH6N+E38R+D7tvOVwDhbFxrInZ7fkOllfePu3KaKsUXbjklgtKUyPREs1LGJC8ffrpCPo1k9BVmwFA4Eg==", "license": "MIT", "peerDependencies": {"@pixi/core": "7.4.0", "@pixi/sprite": "7.4.0"}}, "node_modules/pixi.js/node_modules/@pixi/text-bitmap": {"version": "7.4.0", "resolved": "https://registry.npmjs.org/@pixi/text-bitmap/-/text-bitmap-7.4.0.tgz", "integrity": "sha512-OkYixlqMW9b1EHtEbSP9mgZEqI0WLN1KP4h2EyJk0LC9lH2Ybp3v7ZGHKAetGkSCt8PXY5AfXbcWtm+TgTWbJw==", "license": "MIT", "peerDependencies": {"@pixi/assets": "7.4.0", "@pixi/core": "7.4.0", "@pixi/display": "7.4.0", "@pixi/mesh": "7.4.0", "@pixi/text": "7.4.0"}}, "node_modules/pixi.js/node_modules/@pixi/text-html": {"version": "7.4.0", "resolved": "https://registry.npmjs.org/@pixi/text-html/-/text-html-7.4.0.tgz", "integrity": "sha512-HOSKLynkL4cXQdv7zMst7+vISKp4ueCdJpV2zwQJnwVa/dHKlMULQ4+F5yxbtgAF8fYcH3iNfFLaraFlx1hL5A==", "license": "MIT", "peerDependencies": {"@pixi/core": "7.4.0", "@pixi/display": "7.4.0", "@pixi/sprite": "7.4.0", "@pixi/text": "7.4.0"}}, "node_modules/pixi.js/node_modules/@pixi/ticker": {"version": "7.4.0", "resolved": "https://registry.npmjs.org/@pixi/ticker/-/ticker-7.4.0.tgz", "integrity": "sha512-GaDmk27tEpPfUVgVTNQWGuOYGu6ehqmVSGxecCv4No5KHP52+LihTC4YHO06zRxfyrIOgafooDL/vQiEMqas8g==", "license": "MIT", "dependencies": {"@pixi/extensions": "7.4.0", "@pixi/settings": "7.4.0", "@pixi/utils": "7.4.0"}}, "node_modules/pixi.js/node_modules/@pixi/utils": {"version": "7.4.0", "resolved": "https://registry.npmjs.org/@pixi/utils/-/utils-7.4.0.tgz", "integrity": "sha512-VBnxNGGg/uj7k1wmvyNZei2qpbFNN/kdQ2/mwNXJtFcFymVfijNZWRUNobpSRE/yHx40WGYzSm3ZJZrF4WxFzA==", "license": "MIT", "dependencies": {"@pixi/color": "7.4.0", "@pixi/constants": "7.4.0", "@pixi/settings": "7.4.0", "@types/earcut": "^2.1.0", "earcut": "^2.2.4", "eventemitter3": "^4.0.0", "url": "^0.11.0"}}, "node_modules/pixi.js/node_modules/eventemitter3": {"version": "4.0.7", "resolved": "https://registry.npmjs.org/eventemitter3/-/eventemitter3-4.0.7.tgz", "integrity": "sha512-8guHBZCwKnFhYdHr2ysuRWErTwhoN2X8XELRlrRwpmfeY2jjuUN4taQMsULKUVo1K4DvZl+0pgfyoysHxvmvEw==", "license": "MIT"}, "node_modules/postcss": {"version": "8.5.4", "resolved": "https://registry.npmjs.org/postcss/-/postcss-8.5.4.tgz", "integrity": "sha512-QSa9EBe+uwlGTFmHsPKokv3B/oEMQZxfqW0QqNCyhpa6mB1afzulwn8hihglqAb2pOw+BJgNlmXQ8la2VeHB7w==", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/postcss/"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/postcss"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"nanoid": "^3.3.11", "picocolors": "^1.1.1", "source-map-js": "^1.2.1"}, "engines": {"node": "^10 || ^12 || >=14"}}, "node_modules/punycode": {"version": "1.4.1", "resolved": "https://registry.npmjs.org/punycode/-/punycode-1.4.1.tgz", "integrity": "sha512-jmYNElW7yvO7TV33CjSmvSiE2yco3bV2czu/OzDKdMNVZQWfxCblURLhf+47syQRBntjfLdd/H0egrzIG+oaFQ==", "license": "MIT"}, "node_modules/qs": {"version": "6.14.0", "resolved": "https://registry.npmjs.org/qs/-/qs-6.14.0.tgz", "integrity": "sha512-YWWTjgABSKcvs/nWBi9PycY/JiPJqOD4JA6o9Sej2AtvSGarXxKC3OQSk4pAarbdQlKAh5D4FCQkJNkW+GAn3w==", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"side-channel": "^1.1.0"}, "engines": {"node": ">=0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/react": {"version": "19.1.0", "resolved": "https://registry.npmjs.org/react/-/react-19.1.0.tgz", "integrity": "sha512-FS+XFBNvn3GTAWq26joslQgWNoFu08F4kl0J4CgdNKADkdSGXQyTCnKteIAJy96Br6YbpEU1LSzV5dYtjMkMDg==", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/react-dom": {"version": "19.1.0", "resolved": "https://registry.npmjs.org/react-dom/-/react-dom-19.1.0.tgz", "integrity": "sha512-Xs1hdnE+DyKgeHJeJznQmYMIBG3TKIHJJT95Q58nHLSrElKlGQqDTR2HQ9fx5CN/Gk6Vh/kupBTDLU11/nDk/g==", "license": "MIT", "dependencies": {"scheduler": "^0.26.0"}, "peerDependencies": {"react": "^19.1.0"}}, "node_modules/scheduler": {"version": "0.26.0", "resolved": "https://registry.npmjs.org/scheduler/-/scheduler-0.26.0.tgz", "integrity": "sha512-NlHwttCI/l5gCPR3D1nNXtWABUmBwvZpEQiD4IXSbIDq8BzLIK/7Ir5gTFSGZDUu37K5cMNp0hFtzO38sC7gWA==", "license": "MIT"}, "node_modules/semver": {"version": "7.7.2", "resolved": "https://registry.npmjs.org/semver/-/semver-7.7.2.tgz", "integrity": "sha512-RF0Fw+rO5AMf9MAyaRXI4AV0Ulj5lMHqVxxdSgiVbixSCXoEmmX/jk0CuJw4+3SqroYO9VoUh+HcuJivvtJemA==", "license": "ISC", "optional": true, "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/sharp": {"version": "0.34.2", "resolved": "https://registry.npmjs.org/sharp/-/sharp-0.34.2.tgz", "integrity": "sha512-lszvBmB9QURERtyKT2bNmsgxXK0ShJrL/fvqlonCo7e6xBF8nT8xU6pW+PMIbLsz0RxQk3rgH9kd8UmvOzlMJg==", "hasInstallScript": true, "license": "Apache-2.0", "optional": true, "dependencies": {"color": "^4.2.3", "detect-libc": "^2.0.4", "semver": "^7.7.2"}, "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "optionalDependencies": {"@img/sharp-darwin-arm64": "0.34.2", "@img/sharp-darwin-x64": "0.34.2", "@img/sharp-libvips-darwin-arm64": "1.1.0", "@img/sharp-libvips-darwin-x64": "1.1.0", "@img/sharp-libvips-linux-arm": "1.1.0", "@img/sharp-libvips-linux-arm64": "1.1.0", "@img/sharp-libvips-linux-ppc64": "1.1.0", "@img/sharp-libvips-linux-s390x": "1.1.0", "@img/sharp-libvips-linux-x64": "1.1.0", "@img/sharp-libvips-linuxmusl-arm64": "1.1.0", "@img/sharp-libvips-linuxmusl-x64": "1.1.0", "@img/sharp-linux-arm": "0.34.2", "@img/sharp-linux-arm64": "0.34.2", "@img/sharp-linux-s390x": "0.34.2", "@img/sharp-linux-x64": "0.34.2", "@img/sharp-linuxmusl-arm64": "0.34.2", "@img/sharp-linuxmusl-x64": "0.34.2", "@img/sharp-wasm32": "0.34.2", "@img/sharp-win32-arm64": "0.34.2", "@img/sharp-win32-ia32": "0.34.2", "@img/sharp-win32-x64": "0.34.2"}}, "node_modules/side-channel": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/side-channel/-/side-channel-1.1.0.tgz", "integrity": "sha512-ZX99e6tRweoUXqR+VBrslhda51Nh5MTQwou5tnUDgbtyM0dBgmhEDtWGP/xbKn6hqfPRHujUNwz5fy/wbbhnpw==", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "object-inspect": "^1.13.3", "side-channel-list": "^1.0.0", "side-channel-map": "^1.0.1", "side-channel-weakmap": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel-list": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/side-channel-list/-/side-channel-list-1.0.0.tgz", "integrity": "sha512-FCLHtRD/gnpCiCHEiJLOwdmFP+wzCmDEkc9y7NsYxeF4u7Btsn1ZuwgwJGxImImHicJArLP4R0yX4c2KCrMrTA==", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "object-inspect": "^1.13.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel-map": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/side-channel-map/-/side-channel-map-1.0.1.tgz", "integrity": "sha512-VCjCNfgMsby3tTdo02nbjtM/ewra6jPHmpThenkTYh8pG9ucZ/1P8So4u4FGBek/BjpOVsDCMoLA/iuBKIFXRA==", "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.5", "object-inspect": "^1.13.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel-weakmap": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/side-channel-weakmap/-/side-channel-weakmap-1.0.2.tgz", "integrity": "sha512-WPS/HvHQTYnHisLo9McqBHOJk2FkHO/tlpvldyrnem4aeQp4hai3gythswg6p01oSoTl58rcpiFAjF2br2Ak2A==", "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.5", "object-inspect": "^1.13.3", "side-channel-map": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/simple-swizzle": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/simple-swizzle/-/simple-swizzle-0.2.2.tgz", "integrity": "sha512-JA//kQgZtbuY83m+xT+tXJkmJncGMTFT+C+g2h2R9uxkYIrE2yy9sgmcLhCnw57/WSD+Eh3J97FPEDFnbXnDUg==", "license": "MIT", "optional": true, "dependencies": {"is-arrayish": "^0.3.1"}}, "node_modules/source-map-js": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/source-map-js/-/source-map-js-1.2.1.tgz", "integrity": "sha512-UXWMKhLOwVKb728IUtQPXxfYU+usdybtUrK/8uGE8CQMvrhOpwvzDBwj0QhSL7MQc7vIsISBG8VQ8+IDQxpfQA==", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/streamsearch": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/streamsearch/-/streamsearch-1.1.0.tgz", "integrity": "sha512-Mcc5wHehp9aXz1ax6bZUyY5afg9u2rv5cqQI3mRrYkGC8rW2hM02jWuwjtL++LS5qinSyhj2QfLyNsuc+VsExg==", "engines": {"node": ">=10.0.0"}}, "node_modules/styled-jsx": {"version": "5.1.6", "resolved": "https://registry.npmjs.org/styled-jsx/-/styled-jsx-5.1.6.tgz", "integrity": "sha512-qSVyDTeMotdvQYoHWLNGwRFJHC+i+ZvdBRYosOFgC+Wg1vx4frN2/RG/NA7SYqqvKNLf39P2LSRA2pu6n0XYZA==", "license": "MIT", "dependencies": {"client-only": "0.0.1"}, "engines": {"node": ">= 12.0.0"}, "peerDependencies": {"react": ">= 16.8.0 || 17.x.x || ^18.0.0-0 || ^19.0.0-0"}, "peerDependenciesMeta": {"@babel/core": {"optional": true}, "babel-plugin-macros": {"optional": true}}}, "node_modules/tailwindcss": {"version": "4.1.8", "resolved": "https://registry.npmjs.org/tailwindcss/-/tailwindcss-4.1.8.tgz", "integrity": "sha512-kjeW8gjdxasbmFKpVGrGd5T4i40mV5J2Rasw48QARfYeQ8YS9x02ON9SFWax3Qf616rt4Cp3nVNIj6Hd1mP3og==", "dev": true, "license": "MIT"}, "node_modules/tapable": {"version": "2.2.2", "resolved": "https://registry.npmjs.org/tapable/-/tapable-2.2.2.tgz", "integrity": "sha512-Re10+NauLTMCudc7T5WLFLAwDhQ0JWdrMK+9B2M8zR5hRExKmsRDCBA7/aV/pNJFltmBFO5BAMlQFi/vq3nKOg==", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/tar": {"version": "7.4.3", "resolved": "https://registry.npmjs.org/tar/-/tar-7.4.3.tgz", "integrity": "sha512-5S7Va8hKfV7W5U6g3aYxXmlPoZVAwUMy9AOKyF2fVuZa2UD3qZjg578OrLRt8PcNN1PleVaL/5/yYATNL0ICUw==", "dev": true, "license": "ISC", "dependencies": {"@isaacs/fs-minipass": "^4.0.0", "chownr": "^3.0.0", "minipass": "^7.1.2", "minizlib": "^3.0.1", "mkdirp": "^3.0.1", "yallist": "^5.0.0"}, "engines": {"node": ">=18"}}, "node_modules/tslib": {"version": "2.8.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.8.1.tgz", "integrity": "sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==", "license": "0BSD"}, "node_modules/typescript": {"version": "5.8.3", "resolved": "https://registry.npmjs.org/typescript/-/typescript-5.8.3.tgz", "integrity": "sha512-p1diW6TqL9L07nNxvRMM7hMMw4c5XOo/1ibL4aAIGmSAt9slTE1Xgw5KWuof2uTOvCg9BY7ZRi+GaF+7sfgPeQ==", "dev": true, "license": "Apache-2.0", "bin": {"tsc": "bin/tsc", "tsserver": "bin/tsserver"}, "engines": {"node": ">=14.17"}}, "node_modules/undici-types": {"version": "6.21.0", "resolved": "https://registry.npmjs.org/undici-types/-/undici-types-6.21.0.tgz", "integrity": "sha512-iwDZqg0QAGrg9Rav5H4n0M64c3mkR59cJ6wQp+7C4nI0gsmExaedaYLNO44eT4AtBBwjbTiGPMlt2Md0T9H9JQ==", "dev": true, "license": "MIT"}, "node_modules/unplugin": {"version": "1.16.1", "resolved": "https://registry.npmjs.org/unplugin/-/unplugin-1.16.1.tgz", "integrity": "sha512-4/u/j4FrCKdi17jaxuJA0jClGxB1AvU2hw/IuayPc4ay1XGaJs/rbb4v5WKwAjNifjmXK9PIFyuPiaK8azyR9w==", "license": "MIT", "dependencies": {"acorn": "^8.14.0", "webpack-virtual-modules": "^0.6.2"}, "engines": {"node": ">=14.0.0"}}, "node_modules/unplugin-preprocessor-directives": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/unplugin-preprocessor-directives/-/unplugin-preprocessor-directives-1.0.3.tgz", "integrity": "sha512-l/ZZ7CpVSqzkG3v2zBl2DEur4sC+qUdHpc9TrKahXP+JbPCQYHKUgdCgFpbrM5hLLQqxoa9gVleIZJL/2Lk4Bg==", "license": "MIT", "dependencies": {"@ampproject/remapping": "^2.2.1", "magic-string": "^0.30.5", "unplugin": "^1.6.0"}, "peerDependencies": {"@nuxt/kit": "^3", "@nuxt/schema": "^3", "@rspack/core": "*", "esbuild": "*", "rollup": "^3", "vite": ">=3", "webpack": "^4 || ^5"}, "peerDependenciesMeta": {"@nuxt/kit": {"optional": true}, "@nuxt/schema": {"optional": true}, "@rspack/core": {"optional": true}, "esbuild": {"optional": true}, "rollup": {"optional": true}, "vite": {"optional": true}, "webpack": {"optional": true}}}, "node_modules/url": {"version": "0.11.4", "resolved": "https://registry.npmjs.org/url/-/url-0.11.4.tgz", "integrity": "sha512-oCwdVC7mTuWiPyjLUz/COz5TLk6wgp0RCsN+wHZ2Ekneac9w8uuV0njcbbie2ME+Vs+d6duwmYuR3HgQXs1fOg==", "license": "MIT", "dependencies": {"punycode": "^1.4.1", "qs": "^6.12.3"}, "engines": {"node": ">= 0.4"}}, "node_modules/uuid": {"version": "9.0.1", "resolved": "https://registry.npmjs.org/uuid/-/uuid-9.0.1.tgz", "integrity": "sha512-b+1eJOlsR9K8HJpow9Ok3fiWOWSIcIzXodvv0rQjVoOVNpWMpxf1wZNpt4y9h10odCNrqnYp1OBzRktckBe3sA==", "funding": ["https://github.com/sponsors/broofa", "https://github.com/sponsors/ctavan"], "license": "MIT", "bin": {"uuid": "dist/bin/uuid"}}, "node_modules/webpack-virtual-modules": {"version": "0.6.2", "resolved": "https://registry.npmjs.org/webpack-virtual-modules/-/webpack-virtual-modules-0.6.2.tgz", "integrity": "sha512-66/V2i5hQanC51vBQKPH4aI8NMAcBW59FVBs+rC7eGHupMyfn34q7rZIE+ETlJ+XTevqfUhVVBgSUNSW2flEUQ==", "license": "MIT"}, "node_modules/yallist": {"version": "5.0.0", "resolved": "https://registry.npmjs.org/yallist/-/yallist-5.0.0.tgz", "integrity": "sha512-YgvUTfwqyc7UXVMrB+SImsVYSmTS8X/tSrtdNZMImM+n7+QTriRXyXim0mBrTXNeqzVF0KWGgHPeiyViFFrNDw==", "dev": true, "license": "BlueOak-1.0.0", "engines": {"node": ">=18"}}, "node_modules/zod": {"version": "3.22.4", "resolved": "https://registry.npmjs.org/zod/-/zod-3.22.4.tgz", "integrity": "sha512-iC+8Io04lddc+mVqQ9AZ7OQ2MrUKGN+oIQyq1vemgt46jwCwLfhq7/pwnBnNXXXZb8VTVLKwp9EDkx+ryxIWmg==", "license": "MIT", "funding": {"url": "https://github.com/sponsors/colinhacks"}}}}